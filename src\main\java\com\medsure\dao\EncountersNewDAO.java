package com.medsure.dao;

import java.util.Date;
import java.util.List;

import com.medsure.model.WatchRxEncountersNew;

public interface EncountersNewDAO extends BaseDAO<WatchRxEncountersNew> {

	public List<Object[]> homeVisitsCountWeekPhysician(Long physicianId);

	public List<Object[]> homeVisitsCountMonthPhysician(Long physicianId);

	public List<Object[]> homeVisitsCountYearPhysician(Long physicianId);

	public List<Object[]> homeVisitsCountYearCaseManager(Long clinicianId);

	public List<Object[]> homeVisitsCountMonthCaseManager(Long clinicianId);

	public List<Object[]> homeVisitsCountWeekCaseManager(Long clinicianId);

	public List<WatchRxEncountersNew> reasonByPatientId(Long patientId, String reason, Date startDate, Date endDate);

	public List<WatchRxEncountersNew> encounterMinutesTimeIntervalPatient(Long patientId, Date startDate, Date endDate);

	public List<WatchRxEncountersNew> getAllEncounterByPatientdIdAndDatePagination(Long patientId, Date startDate,
			Date endDate, Integer index, Integer pageSize);

	public List<WatchRxEncountersNew> getMins(Long patientId);

	public List<String> getDeviceTrainingEncounterReasons(Long patientId, Date startDate, Date endDate);

	public List<WatchRxEncountersNew> getEncounterForCaregiverApp(Long patientId, Date startDate, Date endDate,
			Integer index, Integer pageSize, String program);

	boolean existsByPatientAndTimeRange(Long patientId,Date startTime,Date endTime,Long excludeEncounterId);

	boolean existsByUserAndTimeRange(Long userId,Date startTime,Date endTime,Long excludeEncounterId);

	public List<WatchRxEncountersNew> getAllDraftsByPatientIdAndDatePagination(Long patientId, Date startDate, Date endDate, int index, int pageSize);

	public Long countAllDraftsByPatientIdAndDate(Long patientId, Date startDate, Date endDate);
}

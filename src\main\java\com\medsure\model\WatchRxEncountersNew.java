package com.medsure.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@Entity
@Table(name = "WATCHRX_PATIENT_ENCOUNTERS")
public class WatchRxEncountersNew extends Auditable<String> implements java.io.Serializable {
	private static final long serialVersionUID = 1L;
	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "ID", unique = true, nullable = false)
	private Long encountersId;

//	@Column(name = "ENCOUNTER_TITLE")
//	private String encounterTitle;

	@Column(name = "ENCOUNTER_REASON")
	private String encounterReason;

	@Column(name = "ENCOUNTER_DESCRIPTION", columnDefinition = "LONGTEXT")
	private String encounterDescription;

	@Column(name = "DURATION" ,columnDefinition = "DOUBLE DEFAULT 0.0")
	private Double duration;

//	@Column(name = "ENCOUNTER_RESULT")
//	private String encounterResult;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATED_DATE", nullable = false, length = 19)
	private Date encounterDatetime;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATED_END_DATE", nullable = true, length = 19)
	private Date encounterEndDatetime;

	public String getReview() {
		return review;
	}

	public void setReview(String review) {
		this.review = review;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "FK_PATIENT_ID", nullable = false)
	private WatchrxPatient watchrxPatient;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "FK_USER_ID", nullable = false)
	private WatchrxUser watchrxUser;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "FK_PATIENT_ALERT_ID", nullable = true)
	private WatchrxPatientAlert watchrxPatientAlert;

	@Column(name = "REVIEW", length = 1024)
	private String review;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ENCOUNTER_REASON_CODE", referencedColumnName = "CODE")
	private WatchRxEncounterReasons watchRxEncounterReasons;

	@Column(name = "IS_DRAFT", nullable = false, columnDefinition = "tinyint(1) default 0", length = 1)
	private Boolean isDraft = Boolean.FALSE;

	public WatchRxEncounterReasons getWatchRxEncounterReasons() {
		return watchRxEncounterReasons;
	}

	public void setWatchRxEncounterReasons(WatchRxEncounterReasons watchRxEncounterReasons) {
		this.watchRxEncounterReasons = watchRxEncounterReasons;
	}

	public WatchrxPatientAlert getWatchrxPatientAlert() {
		return watchrxPatientAlert;
	}

	public void setWatchrxPatientAlert(WatchrxPatientAlert watchrxPatientAlert) {
		this.watchrxPatientAlert = watchrxPatientAlert;
	}

	public WatchrxUser getWatchrxUser() {
		return watchrxUser;
	}

	public void setWatchrxUser(WatchrxUser watchrxUser) {
		this.watchrxUser = watchrxUser;
	}

	public Long getEncountersId() {
		return encountersId;
	}

	public void setEncountersId(Long encountersId) {
		this.encountersId = encountersId;
	}

//	public String getEncounterTitle() {
//		return encounterTitle;
//	}
//
//	public void setEncounterTitle(String encounterTitle) {
//		this.encounterTitle = encounterTitle;
//	}

	public String getEncounterReason() {
		return encounterReason;
	}

	public void setEncounterReason(String encounterReason) {
		this.encounterReason = encounterReason;
	}

	public String getEncounterDescription() {
		return encounterDescription;
	}

	public void setEncounterDescription(String encounterDescription) {
		this.encounterDescription = encounterDescription;
	}

	public Date getEncounterDatetime() {
		return encounterDatetime;
	}

	public void setEncounterDatetime(Date encounterDatetime) {
		this.encounterDatetime = encounterDatetime;
	}

	public Date getEncounterEndDatetime() {
		return encounterEndDatetime;
	}

	public void setEncounterEndDatetime(Date encounterEndDatetime) {
		this.encounterEndDatetime = encounterEndDatetime;
	}

	public WatchrxPatient getWatchrxPatient() {
		return watchrxPatient;
	}

	public void setWatchrxPatient(WatchrxPatient watchrxPatient) {
		this.watchrxPatient = watchrxPatient;
	}

	public Double getDuration() {
		return duration;
	}

	public void setDuration(Double duration) {
		this.duration = duration;
	}

	public Boolean getIsDraft() {
		return isDraft;
	}

	public void setIsDraft(Boolean isDraft) {
		this.isDraft = isDraft;
	}

//	public String getEncounterResult() {
//		return encounterResult;
//	}
//
//	public void setEncounterResult(String encounterResult) {
//		this.encounterResult = encounterResult;
//	}

}

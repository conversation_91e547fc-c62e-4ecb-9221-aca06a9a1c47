package com.medsure.ui.service.server;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.TimeZone;
import java.util.UUID;
import java.util.stream.Collectors;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.mail.MessagingException;
import javax.mail.internet.AddressException;
import javax.servlet.http.HttpServletRequest;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.appengine.api.appidentity.AppIdentityService;
import com.google.appengine.api.appidentity.AppIdentityServiceFactory;
import com.google.appengine.tools.cloudstorage.GcsFileOptions;
import com.google.appengine.tools.cloudstorage.GcsFilename;
import com.google.appengine.tools.cloudstorage.GcsOutputChannel;
import com.google.appengine.tools.cloudstorage.GcsService;
import com.google.appengine.tools.cloudstorage.GcsServiceFactory;
import com.google.appengine.tools.cloudstorage.RetryParams;
import com.medsure.common.Constants;
import com.medsure.dao.GroupUserAssignmentDAO;
import com.medsure.dao.PatientDAO;
import com.medsure.dao.WatchRxUserPasswordLinkDAO;
import com.medsure.exception.WatchRxException;
import com.medsure.exception.WatchRxExceptionCodes;
import com.medsure.factory.WatchRxFactory;
import com.medsure.model.WatchRxUserPasswordLink;
import com.medsure.model.WatchrxGroupUserAssignment;
import com.medsure.model.WatchrxPatient;
import com.medsure.model.WatchrxStripeEvent;
import com.medsure.model.WatchrxUser;
import com.medsure.service.ClinicianService;
import com.medsure.service.GroupRoleService;
import com.medsure.service.InventoryService;
import com.medsure.service.PatientService;
import com.medsure.service.PaymentService;
import com.medsure.service.PlanManagementService;
import com.medsure.service.StripeWebhookHandlerService;
import com.medsure.service.SubscriptionMangementService;
import com.medsure.service.UserService;
import com.medsure.tenovi.model.TenoviReading;
import com.medsure.ui.entity.caregiver.request.Login;
import com.medsure.ui.entity.caregiver.response.GeneralInfo;
import com.medsure.ui.entity.external.Request.telementary.ForwardStatus;
import com.medsure.ui.entity.patient.request.EncryptedReqRespData;
import com.medsure.ui.entity.server.CaseManagerPatientsAssignVO;
import com.medsure.ui.entity.server.CaseManagerVO;
import com.medsure.ui.entity.server.LoginRequestVO;
import com.medsure.ui.entity.server.LoginStatusVO;
import com.medsure.ui.entity.server.PatientWatchCaregiverListVO;
import com.medsure.ui.entity.server.PhoneCommunicationVO;
import com.medsure.ui.entity.server.PhysicianCaseManagerAssignVO;
import com.medsure.ui.entity.server.PhysicianVO;
import com.medsure.ui.entity.server.RPMPhysicianCaseManagersResponseVO;
import com.medsure.ui.entity.server.RegistrationVO;
import com.medsure.ui.entity.server.StatusVO;
import com.medsure.ui.entity.server.TestFileUploadStatus;
import com.medsure.ui.entity.server.TestFileVO;
import com.medsure.ui.entity.server.UserVO;
import com.medsure.ui.entity.server.WatchrxCaseManagerPatientsAssignmentResponseListVO;
import com.medsure.ui.entity.server.WatchrxPhysicianCaseManagerAssignmentResponseListVO;
import com.medsure.ui.entity.server.WatchInventory.WatchInventoryResponse;
import com.medsure.ui.entity.server.WatchInventory.WatchInventoryResponseVO;
import com.medsure.ui.entity.server.WatchInventory.WatchInventoryVO;
import com.medsure.ui.entity.server.congnita.CognitaData;
import com.medsure.ui.entity.server.createpatientflow.SaveMedicationImageRequestVO;
import com.medsure.ui.entity.server.planmanagement.GetAllWatchrxPlanResponse;
import com.medsure.ui.entity.server.planmanagement.WatchrxPlanVO;
import com.medsure.ui.entity.server.stripeHandlerManagement.StripeHandlerResponse;
import com.medsure.ui.util.JsonUtils;
import com.medsure.ui.util.WatchRxUtils;
import com.medsure.util.AESUtil;
import com.medsure.util.DBEncryptionUtil;
import com.medsure.util.SendMail;
import com.stripe.Stripe;
import com.stripe.exception.ApiConnectionException;
import com.stripe.exception.ApiException;
import com.stripe.exception.AuthenticationException;
import com.stripe.exception.CardException;
import com.stripe.exception.InvalidRequestException;
import com.stripe.model.Charge;
import com.stripe.model.Event;

@Controller
@RequestMapping("")
public class AuthenticationController {

	private static Logger logger = Logger.getLogger(AuthenticationController.class);

	@Autowired
	UserService userService;

	@Autowired
	SendMail email;

	@Autowired
	ClinicianService clinicianService;

	@Autowired
	PaymentService paymentService;

	@Autowired
	StripeWebhookHandlerService stripeEventService;

	@Autowired
	SubscriptionMangementService subscriptionService;

	@Autowired
	InventoryService inventoryService;

	@Autowired
	PlanManagementService planManagementService;

	@Autowired
	PatientService patientService;

	@Autowired
	GroupRoleService groupRoleService;

	@Autowired
	private GroupUserAssignmentDAO grpUsrDao;

	@Autowired
	private WatchRxUserPasswordLinkDAO passwordLinkDAO;

	@Autowired
	private DBEncryptionUtil dbEncryptionUtil;

	@Autowired
	private UserDetailsService jwtInMemoryUserDetailsService;

	@Autowired
	private com.medsure.config.jwt.JwtTokenUtil jwtTokenUtil;

	private static final String API_KEY = "7316da12-df62-4d90-9745-1c080b1af092";

	@Autowired
	PatientDAO patientDAO;

	private static final DateTimeFormatter dateTimeHourFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'");

	@RequestMapping(value = "/plan/getAllPlans", method = RequestMethod.GET)
	public @ResponseBody GetAllWatchrxPlanResponse getAllPlans(HttpServletRequest request) throws Exception {
		GetAllWatchrxPlanResponse response = new GetAllWatchrxPlanResponse();
		try {

			List<WatchrxPlanVO> plans = planManagementService.getAllPlans();
			response.setPlans(plans);
			response.setSuccess(true);
			response.setResponseCode(Constants.ResponseCode.PLANSRETIEVEDSUCCESSFULLY);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.PLANSRETIEVEDSUCCESSFULLY;
			response.setMessages(messages);
			return response;

		} catch (CardException | InvalidRequestException | AuthenticationException | ApiConnectionException
				| ApiException e) {
			logger.error(e.getMessage(), e);
			response.setPlans(null);
			response.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			response.setPlans(null);
			response.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		}

	}

	@RequestMapping(value = "", method = RequestMethod.GET)
	public String index(HttpServletRequest request, ModelMap model) throws Exception {
		return "redirect:/home.html";
	}

	@RequestMapping(value = "/login", method = RequestMethod.POST)
	public @ResponseBody String login(HttpServletRequest request, @RequestParam String userName,
			@RequestParam String password) throws Exception {
		StatusVO statusVO = new StatusVO();
		List<String> errorMsgs = new ArrayList<String>();
		System.out.println("In login method ::: email: " + userName + " password:::" + password);
		UserVO user = userService.getUser(userName);
		logger.info("profile:::: " + user);
		if (user != null) {

			if (user.getPassword().equals(password)) {
				statusVO.setSuccess(true);
				final UserDetails userDetails = jwtInMemoryUserDetailsService.loadUserByUsername(userName);
				final String token = jwtTokenUtil.generateToken(userDetails);
				user.setAuthToken(token);
			} else {
				statusVO.setSuccess(false);
				errorMsgs.add("Invalid User Name/Password");
			}
		} else {
			statusVO.setSuccess(false);
			errorMsgs.add("Invalid User Name/Password");
		}
		request.getSession().setAttribute("user", user);
		statusVO.setMessages(errorMsgs.toArray(new String[errorMsgs.size()]));
		return JsonUtils.toJSON(statusVO);
	}

	// LoginRequestVO
	// @RequestMapping(value = "/testmethod1", method = RequestMethod.POST)
	// public @ResponseBody LoginStatusVO loggingin(HttpServletRequest request,
	// @RequestBody AddressVO addr) throws Exception {
	// LoginStatusVO loginStatusVO = new LoginStatusVO();
	// String[] messages = new String[1];
	// messages[0] = "Hi world";
	// loginStatusVO.setMessages(messages);
	// request.getSession().setAttribute("address", addr);
	// return loginStatusVO;
	// }

	@RequestMapping(value = "/loggingin", method = RequestMethod.POST)
	public @ResponseBody LoginStatusVO loggingin(HttpServletRequest request, @RequestBody LoginRequestVO login)
			throws Exception {
		LoginStatusVO loginStatusVO = new LoginStatusVO();
		List<String> errorMsgs = new ArrayList<String>();
		logger.info("User login : " + login.getUserName());
		UserVO user = null;
		if (login.getRoleId() != null && login.getRoleId() == 6) {
			user = patientService.doPatientLogin(login.getUserName(), login.getPassword());
			logger.info("Patient login result: " + user);
			if (user != null) {
				final UserDetails userDetails = jwtInMemoryUserDetailsService.loadUserByUsername(login.getUserName());
				final String token = jwtTokenUtil.generateToken(userDetails);
				user.setAuthToken(token);
				loginStatusVO.setSuccess(true);
				loginStatusVO.setLoggedinUser(user);
				request.getSession().setAttribute("user", user);
			} else {
				loginStatusVO.setSuccess(false);
				errorMsgs.add("Invalid User Name/Password");
				loginStatusVO.setMessages(errorMsgs.toArray(new String[errorMsgs.size()]));
				loginStatusVO.setLoggedinUser(null);
			}
			return loginStatusVO;
		} else {
			if (login.getRoleId() != null) {
				user = userService.getUserByRole(login.getUserName(), login.getRoleId());
			} else {
				user = userService.getUser(login.getUserName());
			}
			logger.info("Login info:" + user);
			if (user != null) {

				if (user.getAvailStatus() != null && user.getAvailStatus().equalsIgnoreCase("N")) {
					loginStatusVO.setSuccess(false);
					errorMsgs.add("User account suspended/In-active.");
					loginStatusVO.setMessages(errorMsgs.toArray(new String[errorMsgs.size()]));
					loginStatusVO.setLoggedinUser(null);
					return loginStatusVO;
				}
				if (user.isMultiRoleUser()) {
					loginStatusVO.setSuccess(false);
					errorMsgs.add("User account has multiple roles. Please select a valid role.");
					loginStatusVO.setMessages(errorMsgs.toArray(new String[errorMsgs.size()]));
					loginStatusVO.setLoggedinUser(null);
					return loginStatusVO;
				}
				if (user.getPassword().equals(login.getPassword())) {
					loginStatusVO.setSuccess(true);
					final UserDetails userDetails = jwtInMemoryUserDetailsService
							.loadUserByUsername(login.getUserName());
					final String token = jwtTokenUtil.generateToken(userDetails);
					user.setAuthToken(token);
					user.setPassword(null);
					loginStatusVO.setLoggedinUser(user);
					request.getSession().setAttribute("user", user);
					userService.updateLogIn(user.getUserId());
				} else {
					loginStatusVO.setSuccess(false);
					int count = userService.setInvalidLoginCount(login.getUserName());
					if (count > 2) {
						errorMsgs.add("User Account Locked.");
						logger.error("User account locked : " + login.getUserName());
					}
					errorMsgs.add("Invalid User Name/Password");
					loginStatusVO.setLoggedinUser(null);
				}
			} else {
				loginStatusVO.setSuccess(false);
				if (login.getRoleId() != null) {
					errorMsgs.add("Invalid User Name/Password/Role.");
				} else {
					errorMsgs.add("Invalid User Name/Password.");
				}
				loginStatusVO.setLoggedinUser(null);
			}
			loginStatusVO.setMessages(errorMsgs.toArray(new String[errorMsgs.size()]));
			return loginStatusVO;
		}
	}

	@RequestMapping(value = "/postlogin", method = RequestMethod.GET)
	public String postLogin(HttpServletRequest request) throws Exception {

		UserVO user = new ObjectMapper()
				.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
		if (user != null && user.getRoleType() == Constants.UserType.PHYSICIAN) {
			return "redirect:/service/patient/patientSummary";
		} else if (user != null && user.getRoleType() == Constants.UserType.CAREGIVER) {
			return "redirect:/service/patient/patientSummary";
		} else {
			return "redirect:/service/watch/watchSummary";
		}
	}

	@RequestMapping(value = "/logout", method = RequestMethod.GET)
	public String logout(HttpServletRequest request, ModelMap model) throws Exception {
		UserVO user = new ObjectMapper()
				.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
		userService.updateLogOut(user.getUserId());
		request.getSession().removeAttribute("user");
		request.getSession().invalidate();
		return "redirect:/index.jsp";
	}

	@RequestMapping(value = "/forgotPassword", method = RequestMethod.POST)
	public @ResponseBody GeneralInfo forgotPassword(HttpServletRequest request, @RequestBody LoginRequestVO resetData)
			throws Exception {
		GeneralInfo response = new GeneralInfo();
		WatchRxException exception;
		System.out.println("Inside Authentication Service");
		logger.info("inside resetPassword:::: " + resetData);

		Login resetDetails = new Login();
		resetDetails.setLoginId(resetData.getUserName());

		UserVO user = userService.getUser(resetDetails.getLoginId());
		if (user != null) {

			String body = "Hello " + user.getFirstName() + " " + user.getLastName() + "\n"
					+ " You have requested a password reset, please follow the link below to reset your password "
					+ WatchRxUtils.getServingUrl() + "/#!/forgotPasswordReset";

			try {

				email.sendEmail(Constants.Email.ADMINEMAIL, user.getUserName(),
						user.getFirstName() + " " + user.getLastName(), "WatchRX Account Password Changed", body);

			} catch (AddressException e) {
				e.printStackTrace();
				logger.error(e);
			} catch (MessagingException e) {
				e.printStackTrace();
				logger.error(e);
			} catch (UnsupportedEncodingException e) {
				e.printStackTrace();
				logger.error(e);
			}

			response.setResponseCode("001");
			response.setResponseMessage("Operation Sucesssful");
			response.setStatus("Success");
		} else {
			exception = new WatchRxException(WatchRxExceptionCodes.INVALID_EMAILID);
			response.setResponseCode(exception.getErrCode());
			response.setResponseMessage(exception.getErrDesc());
			response.setStatus("Failure");
		}
		System.out.println("Response Status: " + response.getResponseMessage());
		return response;
	}

	@RequestMapping(value = "/resetPassword", method = RequestMethod.POST)
	public @ResponseBody GeneralInfo resetPassword(HttpServletRequest request, @RequestBody LoginRequestVO resetData)
			throws Exception {
		GeneralInfo response = new GeneralInfo();
		WatchRxException exception;
		System.out.println("Inside Authentication Service");
		logger.info("inside resetPassword:::: " + resetData);

		Login resetDetails = new Login();

		UserVO user = userService.getUser(resetDetails.getLoginId());
		if (user != null) {
			// clinicianService.resetPasswordByUserName(resetDetails);

			String body = "Hello " + user.getFirstName() + " " + user.getLastName() + "\n"
					+ " You have requested a password reset, please follow the link below to reset your password "
					+ WatchRxUtils.getServingUrl() + "/#!/resetPassword";

			try {

				email.sendEmail(Constants.Email.ADMINEMAIL, user.getUserName(),
						user.getFirstName() + " " + user.getLastName(), "WatchRX Account Password Changed", body);

			} catch (AddressException e) {
				e.printStackTrace();
				logger.error(e);
			} catch (MessagingException e) {
				e.printStackTrace();
				logger.error(e);
			} catch (UnsupportedEncodingException e) {
				e.printStackTrace();
				logger.error(e);
			}

			response.setResponseCode("001");
			response.setResponseMessage("Operation Sucesssful");
			response.setStatus("Success");
		} else {
			exception = new WatchRxException(WatchRxExceptionCodes.INVALID_EMAILID);
			response.setResponseCode(exception.getErrCode());
			response.setResponseMessage(exception.getErrDesc());
			response.setStatus("Failure");
		}
		System.out.println("Response Status: " + response.getResponseMessage());
		return response;
	}

	@RequestMapping(value = "/changePassword", method = RequestMethod.POST)
	public @ResponseBody GeneralInfo forgotPasswords(HttpServletRequest request, @RequestBody LoginRequestVO resetData)
			throws Exception {
		GeneralInfo response = new GeneralInfo();
		WatchRxException exception;

		logger.info("inside resetPassword:::: " + resetData);

		Login resetDetails = new Login();
		resetDetails.setLoginId(resetData.getUserName());
		resetDetails.setPassword(resetData.getPassword());

		UserVO user = userService.getUser(resetDetails.getLoginId());
//		if (user != null) {
//			patientService.doPatientLogin(resetData.getUserName(), resetData.getPassword());
//		}
		if (user != null) {
			if (userService.isPasswordInHistory(resetData.getUserName(), resetData.getPassword())) {
				response.setResponseCode("001");
				response.setResponseMessage("Last 12 passwords cannot be repeated.");
				response.setStatus("Failure");
				return response;
			} else {
				userService.savePasswordInHistory(resetData.getUserName(), resetData.getPassword());
			}
			clinicianService.resetPasswordByUserName(resetDetails);
			response.setResponseCode("001");
			response.setResponseMessage("Operation Sucesssful");
			response.setStatus("Success");

			if (resetData.getToken() != null) {
				String tokenStr = dbEncryptionUtil.decrypt(resetData.getToken());
				WatchRxUserPasswordLink linInfo = passwordLinkDAO.getLinkByToken(tokenStr);
				if (linInfo != null) {
					linInfo.setIsUsed(true);
					passwordLinkDAO.save(linInfo);
				}
			}
			String newPassword = userService.getUser(resetDetails.getLoginId()).getPassword();

			String body = "Hello  " + user.getFirstName() + "  " + user.getLastName() + "\n"
					+ "This is a confirmation that the password for your " + user.getUserName()
					+ " account with Watchrx just has been changed .here is the password " + newPassword;
			try {
				email.sendEmail(Constants.Email.ADMINEMAIL, user.getUserName(),
						user.getFirstName() + " " + user.getLastName(), "WatchRX Account Password Changed", body);
			} catch (AddressException e) {
				e.printStackTrace();
				logger.error(e);
			} catch (MessagingException e) {
				e.printStackTrace();
				logger.error(e);
			} catch (UnsupportedEncodingException e) {
				e.printStackTrace();
				logger.error(e);
			}
		} else {
			exception = new WatchRxException(WatchRxExceptionCodes.INVALID_EMAILID);
			response.setResponseCode(exception.getErrCode());
			response.setResponseMessage(exception.getErrDesc());
			response.setStatus("Failure");
		}
		System.out.println("Response Status: " + response.getResponseMessage());
		return response;
	}

	/*
	 * @RequestMapping(value = "/mailwithuser", method = RequestMethod.GET) public
	 * void sendEmailWithUser(HttpServletRequest request) throws Exception { Login
	 * resetDetails = new Login(); resetDetails.setLoginId("<EMAIL>");
	 * resetDetails.setPassword("Mayappa@123");
	 * 
	 * UserVO user = userService.getUser(resetDetails.getLoginId());
	 * email.sendEmail(Constants.Email.ADMINEMAIL, "<EMAIL>",
	 * user.getFirstName() + " " + user.getLastName(), "WatchRX Account Creation",
	 * "testing mail with user"); request.getSession().setAttribute("user", user);
	 * logger.info("mailwithuser "+user); }
	 */

	/*
	 * @RequestMapping(value = "/mail", method = RequestMethod.GET) public void
	 * sendEmailWithoutUser(HttpServletRequest request) throws Exception { Login
	 * resetDetails = new Login(); resetDetails.setLoginId("<EMAIL>");
	 * resetDetails.setPassword("Mayappa@123"); UserVO user =
	 * userService.getUser(resetDetails.getLoginId());
	 * email.sendEmail(Constants.Email.ADMINEMAIL, "<EMAIL>",
	 * user.getFirstName() + " " + user.getLastName(), "WatchRX Account Creation",
	 * "testing mail without user"); logger.info("mailwithoutuser "+user); }
	 */

	@RequestMapping(value = "/registerUser", method = RequestMethod.POST)
	public @ResponseBody LoginStatusVO registerUser(HttpServletRequest request, @RequestBody RegistrationVO caregiver)
			throws Exception {
		LoginStatusVO status = new LoginStatusVO();
		String[] msg = new String[2];
		System.out.println("Caregiver Info" + caregiver);
		// System.out.println("Inside Authentication Controller Country "+
		// caregiver.getAddress().getCountry());
		logger.info("inside saveClinician:::: " + caregiver);
		if ((caregiver.getUserName()) != null) {
			/*
			 * if (clinicianService.isUserExists(caregiver)) { msg[0] = "Failure"; msg[1] =
			 * "User already exists"; logger.info("User Name already exists. :::: ");
			 * status.setSuccess(false); System.out.println("Username exists"); } else {
			 */
			if (clinicianService.isUserWithSameRoleExists(caregiver)) {
				msg[0] = "Failure";
				msg[1] = "User with similar role and username already exists";
				status.setSuccess(false);
			} else {
			clinicianService.registerClinician(caregiver);
			UserVO user = userService.getUser(caregiver.getUserName());
			logger.info("Registration successful");
			msg[0] = "Success";
			msg[1] = "Registration Successful. Click Login";
			status.setSuccess(true);
			status.setLoggedinUser(user);
			String adminUsersEmail = userService.getUserById(Constants.Admin.ADMINID).getEmail();

			String body = "Hi " + caregiver.getFirstName() + " " + caregiver.getLastName()
					+ ", Your account has been registered.<NAME_EMAIL> for activating your account.";
			String adminEmailBody = "Hi, A new user '" + caregiver.getFirstName() + " " + caregiver.getLastName()
					+ "' has registered with Watchrx. The user's email id is " + caregiver.getUserName();

			try {
				email.sendEmail(Constants.Email.ADMINEMAIL, caregiver.getUserName(),
						caregiver.getFirstName() + " " + caregiver.getLastName(), "WatchRX Account Creation", body);
				email.sendEmail(Constants.Email.ADMINEMAIL, adminUsersEmail,
						caregiver.getFirstName() + " " + caregiver.getLastName(), "WatchRX Account Creation",
						adminEmailBody);
			} catch (AddressException e) {
				e.printStackTrace();
				logger.error(e);
			} catch (MessagingException e) {
				e.printStackTrace();
				logger.error(e);
			} catch (Exception e) {
				e.printStackTrace();
				logger.error(e);
			}
			request.getSession().setAttribute("user", user);
			}
		}
		else {
			msg[0] = "Failure";
			msg[1] = "User Name is required";
			logger.info("User Name is missing. :::: ");
			status.setSuccess(false);
			// System.out.println("User Name is missing. ::::");
		}
		status.setMessages(msg);
		return status;
	}
	/*
	 * @RequestMapping(value = "/saveClinicianImage", method = RequestMethod.POST)
	 * public @ResponseBody StatusVO saveClinicianImage(HttpServletRequest request,
	 * ClinicianVO caregiver) throws Exception { StatusVO status = new StatusVO();
	 * String[] msg = new String[2]; logger.info("inside saveClinicianImage:::: " +
	 * caregiver.getClinicianId()); if ((caregiver.getClinicianId()) != null) {
	 * if(caregiver.getImageFile().getBytes().length > 0) { System.out.println(
	 * "Created output channel");
	 * outputChannel.write(ByteBuffer.wrap(caregiver.getImageFile().getBytes())) ;
	 * outputChannel.close(); logger.info("Done writing...");
	 * caregiver.setPicPath("clinician/" +
	 * caregiver.getImageFile().getOriginalFilename()); logger.info(
	 * "Picture path: "+caregiver.getPicPath()); caregiver.setFileModified(true); }
	 * catch(IOException e){ msg[0] = "Failure"; msg[1] =
	 * Constants.ResponseString.IMAGEUPLOADFAILURE; logger.error(e); } } else {
	 * logger.info("No image to upload"); if (caregiver.getPicPath() != null &&
	 * !"".equals(caregiver.getPicPath())) { caregiver.setFileModified(false); } }
	 * caregiver.setShift(Constants.Shift.MORNIG); caregiver.setSpeciality(1);
	 * 
	 * System.out.println(caregiver.getAddress().getAddress2()); String address2 =
	 * caregiver.getAddress().getAddress2(); if(address2 == null ||
	 * address2.equalsIgnoreCase("undefined") ){
	 * caregiver.getAddress().setAddress2(null); } if(caregiver.getAltPhoneNumber()
	 * == null || caregiver.getAltPhoneNumber().equalsIgnoreCase("undefined") ){
	 * caregiver.setAltPhoneNumber(null); }
	 * 
	 * clinicianService.saveClinician(caregiver); logger.info(
	 * "Registration successful"); msg[0] = "Success"; msg[1] =
	 * "Registration Successful. Click Login"; status.setSuccess(true);
	 * 
	 * String body = "Hi " + caregiver.getFirstName() + " " +
	 * caregiver.getLastName() +
	 * ", A caregiver account has been created for you at http://10-dot-watchrx-1007.appspot.com. Please login with your email to fill in the rest of your details."
	 * ;
	 * 
	 * try { email.sendEmail(Constants.Email.ADMINEMAIL, caregiver.getUserName(),
	 * caregiver.getFirstName() + " " + caregiver.getLastName(),
	 * "WatchRX Account Creation", body); } catch (AddressException e) {
	 * e.printStackTrace(); logger.error(e); } catch (MessagingException e) {
	 * e.printStackTrace(); logger.error(e); } catch (UnsupportedEncodingException
	 * e) { e.printStackTrace(); logger.error(e); } } } else { msg[0] = "Failure";
	 * msg[1] = "User Name is required"; logger.info("User Name is missing. :::: ");
	 * status.setSuccess(false); //
	 * System.out.println("User Name is missing. ::::"); } status.setMessages(msg);
	 * // System.out.println(caregiver.getUserName()); //
	 * System.out.println(caregiver.getPassword()); return status; }
	 */

	@ResponseBody
	@RequestMapping(consumes = "application/json", produces = "application/json", method = RequestMethod.POST, value = "/refundHandler")
	public String refundHandler(@RequestBody String json) throws Exception {
		logger.debug("In refundhandler");
		Stripe.apiKey = Constants.Stripe.SECRETKEY;

		Event event = Event.GSON.fromJson(json, Event.class);
		logger.debug(event.toJson());

		if (event.getType().equals("charge.refunded")) {
			logger.debug(event.getData().toJson());
			Charge charge = (Charge) event.getData().getObject();
			paymentService.cancelOrderByChargeId(charge.getId());

		}

		return "";
	}

	@ResponseBody
	@RequestMapping(consumes = "application/json", produces = "application/json", method = RequestMethod.POST, value = "/medsure/invoiceupcomingevent")
	public String invoiceupcomingevent(@RequestBody String json) throws Exception {
		logger.error("In invoiceupcomingevent handler");
		Stripe.apiKey = Constants.Stripe.SECRETKEY;

		Event event = Event.GSON.fromJson(json, Event.class);
		logger.error("In Stripe handler for invoiceupcomingevent");
		logger.error(event.toJson());
		return "Response from invoiceupcomingevent handler";
	}

	@ResponseBody
	@RequestMapping(consumes = "application/json", produces = "application/json", method = RequestMethod.POST, value = "/medsure/invoicepaymentsuccededevent")
	public String invoicepaymentsuccededevent(@RequestBody String json) {
		logger.error("In invoicepaymentsuccededevent handler");
		Stripe.apiKey = Constants.Stripe.SECRETKEY;
		StripeHandlerResponse response = new StripeHandlerResponse();

		try {
			Event event = Event.GSON.fromJson(json, Event.class);
			logger.error("Event suppilied to invoicepaymentsuccededevent handler is");
			logger.error(event.toJson());
			Long createdEventLogId;

			createdEventLogId = stripeEventService.handleInvoicePaymentSucceededEvent(event);
			response.setSuccess(true);
			response.setHandled(true);
			response.setEventAppId(createdEventLogId);
			response.setIgnored(false);
			response.setLogged(true);
			String[] messages = new String[1];
			messages[0] = Constants.ResponseString.STRIPEEVENTHANDLED;
			response.setMessages(messages);
			response.setResponseCode(Constants.ResponseCode.STRIPEEVENTHANDLED);
			return response.toString();
		} catch (AuthenticationException | InvalidRequestException | ApiConnectionException | CardException
				| ApiException e) {
			logger.error(e);
			response.setSuccess(false);
			response.setHandled(true);
			response.setEventAppId(null);
			response.setIgnored(false);
			response.setLogged(false);
			String[] messages = new String[1];
			messages[0] = Constants.ResponseString.STRIPEEVENTRECEIVALACKOWLEDGEMENT;
			response.setMessages(messages);
			response.setResponseCode(Constants.ResponseCode.STRIPEEVENTRECEIVALACKOWLEDGEMENT);
			return response.toString();
		} catch (Exception ex) {
			logger.error(ex);
			response.setSuccess(false);
			response.setHandled(true);
			response.setEventAppId(null);
			response.setIgnored(false);
			response.setLogged(false);
			String[] messages = new String[1];
			messages[0] = Constants.ResponseString.STRIPEEVENTRECEIVALACKOWLEDGEMENT;
			response.setMessages(messages);
			response.setResponseCode(Constants.ResponseCode.STRIPEEVENTRECEIVALACKOWLEDGEMENT);
			return response.toString();
		}
	}

	@ResponseBody
	@RequestMapping(consumes = "application/json", produces = "application/json", method = RequestMethod.POST, value = "/medsure/stripeeventlog")
	public String stripeeventlog(@RequestBody String json) throws Exception {
		logger.info("In stripeeventlog handler");
		Stripe.apiKey = Constants.Stripe.SECRETKEY;

		Event event = Event.GSON.fromJson(json, Event.class);
		logger.info("In Stripe handler for stripeeventlog");
		logger.info(event.toJson());

		StripeHandlerResponse response = new StripeHandlerResponse();
		response.setSuccess(true);
		String[] messages = new String[1];

		if (event.getType().equals("invoice.payment_succeeded") || event.getType().equals("invoice.created")
				|| event.getType().equals("invoice.payment_failed") || event.getType().equals("invoice.upcoming")) {

			response.setResponseCode(Constants.ResponseCode.STRIPEEVENTRECEIVALACKOWLEDGEMENT);
			messages[0] = Constants.ResponseString.STRIPEEVENTRECEIVALACKOWLEDGEMENT;
			response.setMessages(messages);
			response.setEventAppId(null);
			response.setHandled(false);
			response.setIgnored(true);
			response.setLogged(false);
			return response.toString();
		} else {
			WatchrxStripeEvent savedEvent = stripeEventService.logStripeEvent(event, false, true);
			response.setResponseCode(Constants.ResponseCode.STRIPEEVENTLOGGED);
			messages[0] = Constants.ResponseString.STRIPEEVENTLOGGED;
			response.setMessages(messages);
			response.setEventAppId(savedEvent.getId());
			response.setHandled(false);
			response.setIgnored(true);
			response.setLogged(true);
			return response.toString();
		}
	}

	@ResponseBody
	@RequestMapping(consumes = "application/json", produces = "application/json", method = RequestMethod.POST, value = "/medsure/invoicefailureevent")
	public String invoicefailureevent(@RequestBody String json) throws Exception {
		logger.error("In invoicefailureevent handler");
		Stripe.apiKey = Constants.Stripe.SECRETKEY;
		StripeHandlerResponse response = new StripeHandlerResponse();

		try {
			Event event = Event.GSON.fromJson(json, Event.class);
			logger.error("In Stripe handler for invoicefailureevent");
			logger.error(event.toJson());
			Long createdEventLogId;

			createdEventLogId = stripeEventService.handleInvoicePaymentFailedEvent(event);
			response.setSuccess(true);
			response.setHandled(true);
			response.setEventAppId(createdEventLogId);
			response.setIgnored(false);
			response.setLogged(true);
			String[] messages = new String[1];
			messages[0] = Constants.ResponseString.STRIPEEVENTHANDLED;
			response.setMessages(messages);
			response.setResponseCode(Constants.ResponseCode.STRIPEEVENTHANDLED);
			return response.toString();
		} catch (AuthenticationException | InvalidRequestException | ApiConnectionException | CardException
				| ApiException e) {
			logger.error(e.getMessage());
			response.setSuccess(false);
			response.setHandled(true);
			response.setEventAppId(null);
			response.setIgnored(false);
			response.setLogged(false);
			String[] messages = new String[1];
			messages[0] = Constants.ResponseString.STRIPEEVENTRECEIVALACKOWLEDGEMENT;
			response.setMessages(messages);
			response.setResponseCode(Constants.ResponseCode.STRIPEEVENTRECEIVALACKOWLEDGEMENT);
			return response.toString();
		} catch (Exception ex) {
			logger.error(ex.getMessage());
			response.setSuccess(false);
			response.setHandled(true);
			response.setEventAppId(null);
			response.setIgnored(false);
			response.setLogged(false);
			String[] messages = new String[1];
			messages[0] = Constants.ResponseString.STRIPEEVENTRECEIVALACKOWLEDGEMENT;
			response.setMessages(messages);
			response.setResponseCode(Constants.ResponseCode.STRIPEEVENTRECEIVALACKOWLEDGEMENT);
			return response.toString();
		}

	}

	@ResponseBody
	@RequestMapping(consumes = "application/json", produces = "application/json", method = RequestMethod.POST, value = "/medsure/invoicecreationevent")
	public String invoicecreationevent(@RequestBody String json) throws Exception {
		logger.error("In invoicecreationevent handler");
		Stripe.apiKey = Constants.Stripe.SECRETKEY;

		Event event = Event.GSON.fromJson(json, Event.class);
		logger.error("In Stripe handler for invoicecreationevent");
		logger.error(event.toJson());
		return "Response from invoicecreationevent handler";
	}

	@RequestMapping(value = "/testfileupload", method = RequestMethod.POST)
	public @ResponseBody TestFileUploadStatus saveTestFile(HttpServletRequest request, TestFileVO apk)
			throws Exception {

		TestFileUploadStatus result = new TestFileUploadStatus();

		if (apk.getFile().getBytes().length > 0) {

			GcsService gcsService = GcsServiceFactory.createGcsService(RetryParams.getDefaultInstance());
			AppIdentityService appIdentityService = AppIdentityServiceFactory.getAppIdentityService();
			String defaultBucketName = appIdentityService.getDefaultGcsBucketName();

			SimpleDateFormat sdf = new SimpleDateFormat("MM-dd-yyyy'_'hh-mm-ss");
			sdf.setTimeZone(TimeZone.getTimeZone("US/Eastern"));
			Random random = new Random();
			String randomFileName;
			if (apk.getImei() != null && apk.getImei().length() > 0) {
				randomFileName = String.format("%s%s%s%s", apk.getImei(), "_", sdf.format(new Date()), ".txt");
			} else {
				randomFileName = String.format("%s%s%s%s%s", random.nextInt(9), "_", sdf.format(new Date()), ".txt");
			}

			// watchcrashlogs
			String bucketFolder = "watchcrashlogs";
			if (apk.getLogtype() != null && apk.getLogtype().length() > 0) {
				if (apk.getLogtype().trim().toUpperCase().equals("CR")) {
					bucketFolder = "watchcrashlogs";
				} else if (apk.getLogtype().trim().toUpperCase().equals("OD")) {
					bucketFolder = "watchondemandlogs";
				} else {
					bucketFolder = "watchcrashlogs";
				}
			}

			GcsFilename fileName = new GcsFilename(defaultBucketName + "/" + bucketFolder, randomFileName);

			GcsOutputChannel outputChannel = gcsService.createOrReplace(fileName,
					new GcsFileOptions.Builder().acl("public-read").build());

			outputChannel.write(ByteBuffer.wrap(apk.getFile().getBytes()));
			outputChannel.close();
			logger.info("Done writing...");

			result.setSuccess(true);
			result.setPath(WatchRxUtils.readTextFileOnly(bucketFolder + "/" + randomFileName));
			return result;
		}

		result.setSuccess(false);
		result.setPath("");
		return result;

	}

	/*
	 * @RequestMapping(value = "/testxmlfileupload", method = RequestMethod.POST)
	 * public @ResponseBody TestFileUploadStatus saveTestXMLFile(HttpServletRequest
	 * request, TestFileVO apk) throws Exception {
	 * 
	 * TestFileUploadStatus result = new TestFileUploadStatus();
	 * 
	 * if (apk.getFile() != null && apk.getFile().getBytes().length > 0) {
	 * 
	 * try { DocumentBuilderFactory dbFactory =
	 * DocumentBuilderFactory.newInstance(); DocumentBuilder dBuilder =
	 * dbFactory.newDocumentBuilder(); Document doc =
	 * dBuilder.parse(apk.getFile().getInputStream());
	 * 
	 * doc.getDocumentElement().normalize(); logger.info("Root element :" +
	 * doc.getDocumentElement().getNodeName());
	 * 
	 * NodeList nList = doc.getElementsByTagName("staff");
	 * 
	 * logger.info("----------------------------");
	 * 
	 * for (int temp = 0; temp < nList.getLength(); temp++) {
	 * 
	 * Node nNode = nList.item(temp);
	 * 
	 * logger.info("\nCurrent Element :" + nNode.getNodeName());
	 * 
	 * if (nNode.getNodeType() == Node.ELEMENT_NODE) {
	 * 
	 * Element eElement = (Element) nNode;
	 * 
	 * logger.info("Staff id : " + eElement.getAttribute("id"));
	 * logger.info("First Name : " +
	 * eElement.getElementsByTagName("firstname").item(0).getFirstChild().
	 * getNodeValue()); logger.info("Last Name : " +
	 * eElement.getElementsByTagName("lastname").item(0).getFirstChild().
	 * getNodeValue()); logger.info("Nick Name : " +
	 * eElement.getElementsByTagName("nickname").item(0).getFirstChild().
	 * getNodeValue()); logger.info("Salary : " +
	 * eElement.getElementsByTagName("salary").item(0).getFirstChild().getNodeValue(
	 * ));
	 * 
	 * } } } catch (Exception e) { e.printStackTrace(); } }
	 * 
	 * result.setSuccess(false); result.setPath(""); return result;
	 * 
	 * }
	 */

	@RequestMapping(value = "/inventory", method = RequestMethod.GET)
	public @ResponseBody WatchInventoryResponse getAllInventory(HttpServletRequest request) throws Exception {

		WatchInventoryResponse response = new WatchInventoryResponse();
		List<WatchInventoryVO> inventoryVOList = inventoryService.getCustomInventoryList();
		Iterator<WatchInventoryVO> inventoryVOListIterator = inventoryVOList.iterator();
		WatchInventoryVO inventoryVO;
		WatchInventoryResponseVO inventoryResponseVO;
		List<WatchInventoryResponseVO> inventoryResponseVOs = new ArrayList<WatchInventoryResponseVO>();

		while (inventoryVOListIterator.hasNext()) {

			inventoryVO = inventoryVOListIterator.next();

			inventoryResponseVO = new WatchInventoryResponseVO();
			inventoryResponseVO.setInventoryId(inventoryVO.getInventoryId());
			inventoryResponseVO.setColor(inventoryVO.getColor());
			inventoryResponseVO.setImgUrl(inventoryVO.getImgUrl());
			inventoryResponseVO.setInventoryId(inventoryVO.getInventoryId());
//			inventoryResponseVO.setMakeAndModel(inventoryVO.getMakeAndModel());
			inventoryResponseVO.setMake(inventoryVO.getMake());
			inventoryResponseVO.setModel(inventoryVO.getModel());

			inventoryResponseVO.setQuantity(inventoryVO.getQuantity());
			inventoryResponseVO.setSellingPrice(inventoryVO.getSellingPrice());
			inventoryResponseVO.setSpecUrl(inventoryVO.getSpecUrl());
			inventoryResponseVO.setForsale(inventoryVO.getForsale());
			inventoryResponseVO.setListPrice(inventoryVO.getListPrice());

			inventoryResponseVOs.add(inventoryResponseVO);
		}

		response.setInventory(inventoryResponseVOs);
		response.setSuccess(true);
		response.setResponseCode(Constants.ResponseCode.INVENTORYITEMSOBTAINEDSUCCESSFULLY);
		String messages[] = new String[1];
		messages[0] = Constants.ResponseString.INVENTORYITEMSOBTAINEDSUCCESSFULLY;
		response.setMessages(messages);
		return response;

	}

	@RequestMapping(value = "/charge", method = RequestMethod.POST)
	public @ResponseBody StatusVO charge(HttpServletRequest request) throws Exception {

		StatusVO result = new StatusVO();
		Stripe.apiKey = Constants.Stripe.SECRETKEY;
		String token = request.getParameter("stripeToken");

		Map<String, Object> params = new HashMap<String, Object>();
		params.put("amount", 1000);
		params.put("currency", "usd");
		params.put("description", "Example charge");
		params.put("source", token);

		try {
			Charge.create(params);
			result.setSuccess(true);
			return result;
		} catch (AuthenticationException ex) {
			logger.error(ex.getMessage());
		} catch (InvalidRequestException ex) {
			logger.error(ex.getMessage());
		} catch (ApiConnectionException ex) {
			logger.error(ex.getMessage());
		} catch (CardException ex) {
			logger.error(ex.getMessage());
		} catch (ApiException ex) {
			logger.error(ex.getMessage());
		}
		logger.info("Stripe charge made");
		result.setSuccess(false);
		return result;

	}

	@RequestMapping(value = "/getAllRPMPhysicianCaseManagers/{index}/{pageSize}", method = RequestMethod.GET)
	public @ResponseBody RPMPhysicianCaseManagersResponseVO getAllRPMPhysicianCaseManagers(HttpServletRequest request,
			@PathVariable Integer index, @PathVariable Integer pageSize)
			throws JsonMappingException, JsonProcessingException {
		UserVO user = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		RPMPhysicianCaseManagersResponseVO rPMPhysicianCaseManagersResponseVO = new RPMPhysicianCaseManagersResponseVO();
		if (user != null) {
			rPMPhysicianCaseManagersResponseVO = clinicianService.getAllRPMPhysicianCaseManagers(index, pageSize);
			rPMPhysicianCaseManagersResponseVO.setCountResultCaseManager(clinicianService.getAllRPMCaseManagersCount());
			String messages[] = new String[1];
			messages[0] = "Operation Successful";
			rPMPhysicianCaseManagersResponseVO.setMessages(messages);
			rPMPhysicianCaseManagersResponseVO.setSuccess(true);
		} else {
			String messages[] = new String[1];
			messages[0] = "Operation Failed";
			rPMPhysicianCaseManagersResponseVO.setMessages(messages);
			rPMPhysicianCaseManagersResponseVO.setSuccess(false);
		}

		return rPMPhysicianCaseManagersResponseVO;
	}

	@RequestMapping(value = "/getAllRPMPhysicianCaseManagersForPatient/{index}/{pageSize}", method = RequestMethod.GET)
	public @ResponseBody RPMPhysicianCaseManagersResponseVO getAllRPMPhysicianCaseManagersForPatient(
			HttpServletRequest request, @PathVariable Integer index, @PathVariable Integer pageSize)
			throws JsonMappingException, JsonProcessingException {
		UserVO user = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		RPMPhysicianCaseManagersResponseVO rPMPhysicianCaseManagersResponseVO = new RPMPhysicianCaseManagersResponseVO();
		if (user != null) {
			Set<String> userName = new HashSet<>();
			// List<Long> grpsNam = new ArrayList<>();
			List<WatchrxGroupUserAssignment> users = grpUsrDao.findByProperty("watchrxGroup.groupId", user.getOrgId());
			users.forEach(u1 -> {
				userName.add(u1.getWatchrxUser().getEmail());
			});
			rPMPhysicianCaseManagersResponseVO = clinicianService.getAllRPMPhysicianCaseManagers(index, pageSize);

			List<PhysicianVO> pys = rPMPhysicianCaseManagersResponseVO.getPhysicianVOList().stream()
					.filter(a -> userName.contains(a.getEmail())).collect(Collectors.toList());

			List<CaseManagerVO> cls = rPMPhysicianCaseManagersResponseVO.getClinicianVOList().stream()
					.filter(a -> (userName.contains(a.getEmail()))).collect(Collectors.toList());
			rPMPhysicianCaseManagersResponseVO.setPhysicianVOList(pys);
			rPMPhysicianCaseManagersResponseVO.setClinicianVOList(cls);
			rPMPhysicianCaseManagersResponseVO.setCountResultCaseManager(clinicianService.getAllRPMCaseManagersCount());
			String messages[] = new String[1];
			messages[0] = "Operation Successful";
			rPMPhysicianCaseManagersResponseVO.setMessages(messages);
			rPMPhysicianCaseManagersResponseVO.setSuccess(true);
		} else {
			String messages[] = new String[1];
			messages[0] = "Operation Failed";
			rPMPhysicianCaseManagersResponseVO.setMessages(messages);
			rPMPhysicianCaseManagersResponseVO.setSuccess(false);
		}
		return rPMPhysicianCaseManagersResponseVO;
	}

	@RequestMapping(value = "/assignCaseManagerToPhysician", method = RequestMethod.POST)
	public @ResponseBody WatchrxPhysicianCaseManagerAssignmentResponseListVO assignCaseManagerToPhysician(
			HttpServletRequest request, @RequestBody PhysicianCaseManagerAssignVO physicianCaseManagerAssignVO)
			throws JsonMappingException, JsonProcessingException {
		UserVO user = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		WatchrxPhysicianCaseManagerAssignmentResponseListVO result = new WatchrxPhysicianCaseManagerAssignmentResponseListVO();
		if (user != null) {
			result = clinicianService.assignCaseManagerToPhysician(physicianCaseManagerAssignVO);
		} else {
			String messages[] = new String[1];
			messages[0] = "Operation Failed";
			result.setMessages(messages);
			result.setSuccess(false);
		}

		return result;
	}

	@RequestMapping(value = "/assignCaseManagerToPatients", method = RequestMethod.POST)
	public @ResponseBody WatchrxCaseManagerPatientsAssignmentResponseListVO assignCaseManagerToPatients(
			HttpServletRequest request, @RequestBody CaseManagerPatientsAssignVO caseManagerPatientsAssignVO)
			throws JsonMappingException, JsonProcessingException {
		UserVO user = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		WatchrxCaseManagerPatientsAssignmentResponseListVO result = new WatchrxCaseManagerPatientsAssignmentResponseListVO();
		if (user != null) {
			result = clinicianService.assignCaseManagerToPatients(caseManagerPatientsAssignVO);
		} else {
			String messages[] = new String[1];
			messages[0] = "Operation Failed";
			result.setMessages(messages);
			result.setSuccess(false);
		}

		return result;
	}

	@RequestMapping(value = "/getAllPatientsForCaseManager", method = RequestMethod.POST)
	public @ResponseBody PatientWatchCaregiverListVO getAllPatientsForCaseManager(HttpServletRequest request,
			@RequestBody CaseManagerVO watchrxClinician) throws JsonMappingException, JsonProcessingException {
		UserVO user = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);

		PatientWatchCaregiverListVO result = new PatientWatchCaregiverListVO();

		if (user != null) {
			result = clinicianService.getPatientsListForCaseManager(watchrxClinician);
		} else {
			String messages[] = new String[1];
			messages[0] = "Operation Failed";
			result.setMessages(messages);
			result.setSuccess(false);
		}

		return result;
	}

	@RequestMapping(value = "/unAssignCaseManagerToPatients", method = RequestMethod.POST)
	public @ResponseBody WatchrxCaseManagerPatientsAssignmentResponseListVO unAssignCaseManagerToPatients(
			HttpServletRequest request, @RequestBody CaseManagerPatientsAssignVO caseManagerPatientsAssignVO)
			throws JsonMappingException, JsonProcessingException {
		UserVO user = null;
		user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),
				UserVO.class);
		WatchrxCaseManagerPatientsAssignmentResponseListVO result = new WatchrxCaseManagerPatientsAssignmentResponseListVO();
		if (user != null) {
			result = clinicianService.unAssignCaseManagerToPatients(caseManagerPatientsAssignVO);
		} else {
			String messages[] = new String[1];
			messages[0] = "Operation Failed";
			result.setMessages(messages);
			result.setSuccess(false);
		}

		return result;
	}

	@RequestMapping(value = "/saveMedicationImage", method = RequestMethod.POST)
	public @ResponseBody StatusVO saveMedicationImage(HttpServletRequest request,
			SaveMedicationImageRequestVO patientImage) throws Exception {
		StatusVO response = new StatusVO();
		try {
			return WatchRxFactory.getPatientService().saveMedicationImage(patientImage);

		} catch (Exception e) {
			e.printStackTrace();
			response.setResponseCode(Constants.ResponseCode.INTERNALERROR);
			String messages[] = new String[1];
			messages[0] = Constants.ResponseString.INTERNALERROR;
			response.setMessages(messages);
			response.setSuccess(false);
			return response;
		}
	}

	@PostMapping(value = "/forwardstatus")
	public @ResponseBody Map<String, String> forwardstatus(@RequestBody ForwardStatus wdRequest) throws Exception {
		Map<String, String> status = new HashMap<>();
		try {
			WatchRxFactory.getPatientService().saveWirelessPatientTimeZoneByImei(wdRequest);
			status.put("msg", "success");
			status.put("code", "200");

		} catch (Exception e) {
			e.printStackTrace();
			status.put("msg", "failed");
			status.put("code", "500");
		}
		return status;

	}

	@RequestMapping(value = "/tenoviData", method = RequestMethod.POST)
	public @ResponseBody Map<String, String> syncDataForSmartMeter(HttpServletRequest request,
			@RequestBody List<TenoviReading> reading) throws Exception {
		Map<String, String> status = new HashMap<>();
		try {
			System.out.println("----------->" + reading.toString());
			WatchRxFactory.getPatientService().saveTenoviPatientData(reading);
			status.put("msg", "success");
			status.put("code", "200");
		} catch (Exception e) {
			e.printStackTrace();
			status.put("msg", "failed");
			status.put("code", "500");
		}
		return status;
	}

	@RequestMapping(value = "/api/cognitalabscapmedic", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
	public @ResponseBody ResponseEntity<Map<String, Object>> processRequest(@RequestBody CognitaData requestBody,
			@RequestHeader("API-Key") String apiKey) {
		Map<String, Object> resp = new HashMap<>();
		if (!apiKey.equals(API_KEY)) {
			resp.put("error", "Invalid API Key");
			return new ResponseEntity<>(resp, HttpStatus.UNAUTHORIZED);
		}
		logger.info("requestBody :" + requestBody);
		patientService.processCognitaWirelessDevieData(requestBody);
		resp.put("message", "Successfully received request");
		return new ResponseEntity<>(resp, HttpStatus.OK);
	}

	private static String encodeUrl(String str) throws UnsupportedEncodingException {
		return URLEncoder.encode(str, "UTF-8");
	}

	@RequestMapping(value = "/forgotPasswordLink", method = RequestMethod.POST)
	public @ResponseBody Map<String, Object> forgotPasswordLink(HttpServletRequest request,
			@RequestBody LoginRequestVO resetData) throws Exception {
		Map<String, Object> response = new HashMap<>();
		WatchRxException exception;
		logger.info("inside forgotPasswordLink:::: " + resetData);
		if (resetData.getUserName() != null) {
			UserVO user = userService.getUser(resetData.getUserName());
			WatchRxUserPasswordLink link = passwordLinkDAO.getActiveLinkByUser(resetData.getUserName());
			if (link == null || !isValidLink(link.getCreatedDate(), link.getDuration())) {
				logger.info("New Link going to generate:---");
				link = new WatchRxUserPasswordLink();
				link.setDuration(30l);
				link.setIsExpired(false);
				link.setIsUsed(false);
				UUID uuid = UUID.randomUUID();
				link.setToken(uuid.toString());
				WatchrxUser watchrxUser = new WatchrxUser();
				watchrxUser.setUserId(user.getUserId());
				link.setWatchrxUser(watchrxUser);
				link = passwordLinkDAO.save(link);
			}

			String token = dbEncryptionUtil.encrypt(link.getToken());

			String route = WatchRxUtils.getServingUrl() + "/#!/forgotUserPasswordReset?token=" + encodeUrl(token);
			logger.info("Route:---" + route);
			String htmlContent = "<html><body> <p>Hi " + user.getFirstName() + " " + user.getLastName() + ",</p>"
					+ "<p>Here is a one-time use link to reset your password to access your watchRx account:</p>"
					+ "<p><a href=" + route + ">Reset Password Link</a></p><p><strong>Please Note:</strong> "
					+ "<em>This password reset link will expire in 30 minutes and may only be used once. "
					+ "If you need to get a new link, please use the Forgot Password feature again.</em></p>"
					+ "</body></html>";
			try {
				email.sendPasswordEmail(Constants.Email.ADMINEMAIL, user.getUserName(),
						user.getFirstName() + " " + user.getLastName(), "WatchRx Account Password Link", htmlContent);
			} catch (AddressException e) {
				e.printStackTrace();
				logger.error(e);
			} catch (MessagingException e) {
				e.printStackTrace();
				logger.error(e);
			} catch (UnsupportedEncodingException e) {
				e.printStackTrace();
				logger.error(e);
			}
			response.put("responseCode", "001");
			response.put("responseMessage", "Operation Sucesssful");
			response.put("status", true);
		} else {
			exception = new WatchRxException(WatchRxExceptionCodes.INVALID_EMAILID);
			response.put("responseCode", exception.getErrCode());
			response.put("responseMessage", exception.getErrDesc());
			response.put("status", false);
		}
		return response;
	}

	@RequestMapping(value = "/getTokenInfo", method = RequestMethod.POST)
	public @ResponseBody Map<String, Object> getTokenInfo(HttpServletRequest request,
			@RequestBody Map<String, String> requestBody) {
		Map<String, Object> response = new HashMap<>();
		try {
			if (requestBody.get("token") == null) {
				response.put("message", "Link already expired, Please try again!!");
				response.put("status", false);
				return response;
			}
			String tokenStr = dbEncryptionUtil.decrypt(requestBody.get("token"));
			logger.info("Token:" + tokenStr);
			WatchRxUserPasswordLink linkInfo = passwordLinkDAO.getLinkByToken(tokenStr);
			if (linkInfo == null) {
				response.put("message", "Invalid token information");
				response.put("status", false);
				return response;
			}
			if (linkInfo.getIsExpired()) {
				response.put("message", "Link already expired, Please try again!!");
				response.put("status", false);
				return response;
			}
			if (linkInfo.getIsUsed()) {
				response.put("message", "Link already has been used !!");
				response.put("status", false);
				return response;
			}
			if (!isValidLink(linkInfo.getCreatedDate(), linkInfo.getDuration())) {
				response.put("message", "Link already expired, Please try again!!");
				response.put("status", false);
				return response;
			}
			response.put("userId", linkInfo.getWatchrxUser().getUserId());
			response.put("userName", linkInfo.getWatchrxUser().getUserName());
			response.put("status", true);
			return response;
		} catch (Exception e) {
			e.printStackTrace();
			response.put("status", false);
			response.put("message", "Failed to get link info.");
		}
		return response;
	}

	public static boolean isValidLink(Date dbDate, Long duration) throws Exception {
		Date currentDate = new Date();
		long timeDifference = currentDate.getTime() - dbDate.getTime();
		long minutesDifference = timeDifference / (60 * 1000);
		return minutesDifference < duration;
	}

	@RequestMapping(value = "/v2/testfileupload", method = RequestMethod.POST)
	public @ResponseBody EncryptedReqRespData saveTestFileV2(HttpServletRequest request,
			EncryptedReqRespData requestData) throws Exception {
		EncryptedReqRespData respData = new EncryptedReqRespData();
		TestFileVO apk = new ObjectMapper().readValue(new DBEncryptionUtil().decrypt(requestData.getData()),
				TestFileVO.class);
		respData.setData(new DBEncryptionUtil()
				.encrypt(new ObjectMapper().setSerializationInclusion(JsonInclude.Include.NON_NULL)
						.writeValueAsString(saveTestFile(request, apk))));
		return respData;
	}

	@RequestMapping(value = "/v2/saveMedicationImage", method = RequestMethod.POST)
	public @ResponseBody EncryptedReqRespData saveMedicationImageV2(HttpServletRequest request,
			EncryptedReqRespData requestData) throws Exception {
		EncryptedReqRespData respData = new EncryptedReqRespData();
		SaveMedicationImageRequestVO patientImage = new ObjectMapper()
				.readValue(new DBEncryptionUtil().decrypt(requestData.getData()), SaveMedicationImageRequestVO.class);
		respData.setData(new DBEncryptionUtil()
				.encrypt(new ObjectMapper().setSerializationInclusion(JsonInclude.Include.NON_NULL)
						.writeValueAsString(saveMedicationImage(request, patientImage))));
		return respData;
	}

	private static final String VERIFICATION_TOKEN = "CMECAzMdQJmKQO-ab2P8MA";
	private ObjectMapper objectMapper = new ObjectMapper();;

	@SuppressWarnings("unchecked")
	@RequestMapping(value = "/zoomCall", method = RequestMethod.POST)
	public ResponseEntity<Object> handleZoomWebhook(HttpServletRequest request) throws Exception {
		String timestamp = request.getHeader("x-zm-request-timestamp");
		String signatureHeader = request.getHeader("x-zm-signature");
		logger.info("timestamp :" + timestamp + "signatureHeader: " + signatureHeader);
		// Read raw body
		String body = StreamUtils.copyToString(request.getInputStream(), StandardCharsets.UTF_8);
		Map<String, Object> bodyMap = objectMapper.readValue(body, Map.class);

		logger.info("bodyMap:" + bodyMap);

		String message = String.format("v0:%s:%s", timestamp, body);
		String hash = hmacSha256(message, VERIFICATION_TOKEN);
		String expectedSignature = "v0=" + hash;

		logger.info("expectedSignature: " + expectedSignature + " -- " + "signatureHeader: " + signatureHeader);

		if (!expectedSignature.equals(signatureHeader)) {
			return ResponseEntity.status(401).body(Map.of("message", "Unauthorized request to Zoom Webhook"));
		}

		String event = (String) bodyMap.get("event");
		Map<String, Object> payload = (Map<String, Object>) bodyMap.get("payload");
		logger.info("event:" + event + " payload:" + payload);
		if ("endpoint.url_validation".equals(event)) {
			String plainToken = (String) payload.get("plainToken");
			String encryptedToken = hmacSha256(plainToken, VERIFICATION_TOKEN);

			logger.info("Response:" + Map.of("plainToken", plainToken, "encryptedToken", encryptedToken));
			return ResponseEntity.ok().header(HttpHeaders.CONTENT_TYPE, "application/json")
					.body(Map.of("plainToken", plainToken, "encryptedToken", encryptedToken.toLowerCase()));

		}

		if ("session.ended".equals(event)) {
			Map<String, Object> session = (Map<String, Object>) payload.get("object");

			String sessionKey = (String) session.get("session_key");
			String startTimeStr = (String) session.get("start_time");
			String endTimeStr = (String) session.get("end_time");

			String[] parts = sessionKey.split("_");
			String patientId = parts[0];
			String program = parts[1];

			logger.info("Patient patientId: " + patientId);
			logger.info("Patient Program: " + program);

			Instant start = Instant.parse(startTimeStr);
			Instant end = Instant.parse(endTimeStr);
			long durationInSeconds = Duration.between(start, end).getSeconds();

			String patientTimezone = patientDAO.getTimeZoneByPatientId(Long.valueOf(patientId));
			logger.info("Patient TimeZone: " + patientTimezone);
			if (patientTimezone == null || patientTimezone.isEmpty()) {
				patientTimezone = "UTC";
			}

			// Convert Instants to ZonedDateTime using patient timezone
			ZoneId zoneId = ZoneId.of(patientTimezone);
			ZonedDateTime zonedStart = start.atZone(zoneId);
			ZonedDateTime zonedEnd = end.atZone(zoneId);

			// Format as "yyyy-MM-dd HH:mm:ss"
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

			PhoneCommunicationVO phoneCommunicationVO = new PhoneCommunicationVO();
			phoneCommunicationVO.setPatientId(Long.valueOf(patientId));
			phoneCommunicationVO.setReview(program != null ? program : "rpm");
			phoneCommunicationVO.setNote("Video call Duration from WatchRx Zoom");

			phoneCommunicationVO.setCallStartTime(zonedStart.format(formatter));
			phoneCommunicationVO.setTimeStamp(zonedEnd.format(formatter));
			double durationInMinutes = durationInSeconds / 60.0;
			phoneCommunicationVO.setDuration(durationInMinutes);
			logger.info("Saving data is:" + phoneCommunicationVO.toString());
			patientService.savePhoneCommunication(phoneCommunicationVO);

			WatchrxPatient patient = patientDAO.getById(Long.valueOf(patientId));
			if (patient != null) {
				patient.setIsVideoCallActive("N");
				patientDAO.save(patient);
			}
			return new ResponseEntity<>("success", HttpStatus.OK);
		}

		return ResponseEntity.ok(Map.of("message", "Authorized request to Zoom Webhook"));
	}

	public LocalDateTime getLocalDateTimeForTheZone(ZonedDateTime sourceTime, String targetTimeZone) {
		logger.info("Converting time : a" + sourceTime + " Time zone: " + targetTimeZone);
		return sourceTime.withZoneSameInstant(ZoneId.of(targetTimeZone)).toLocalDateTime();
	}

	private String hmacSha256(String data, String secret) throws Exception {
		Mac hmacSha256 = Mac.getInstance("HmacSHA256");
		SecretKeySpec secretKey = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
		hmacSha256.init(secretKey);
		byte[] hash = hmacSha256.doFinal(data.getBytes(StandardCharsets.UTF_8));
		return bytesToHex(hash).toLowerCase();
	}

	private String bytesToHex(byte[] bytes) {
		StringBuilder hexStr = new StringBuilder();
		for (byte b : bytes) {
			hexStr.append(String.format("%02x", b));
		}
		return hexStr.toString();
	}
	
	@RequestMapping(value = "/saveMedicationPrescription", method = RequestMethod.POST)
	public @ResponseBody StatusVO saveMedicationPrescription(HttpServletRequest request, @RequestParam("file") MultipartFile file, @RequestParam("patientId") Long patientId,
			 @RequestParam("userId") Long userId, @RequestParam("orgId") Long orgId)
			throws Exception {
		logger.info("inside suploadAudio:::: ");
		StatusVO response = new StatusVO();
		try {
			UserVO vo = new UserVO();
			vo.setUserId(patientId);
			vo.setOrgId(orgId);
				return patientService.uploadMedicationPrescription(file, patientId, vo);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return response;

	}
}

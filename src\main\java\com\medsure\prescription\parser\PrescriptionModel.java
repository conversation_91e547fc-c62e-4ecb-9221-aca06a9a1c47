package com.medsure.prescription.parser;

public class PrescriptionModel {

	@Override
	public String toString() {
		return "PrescriptionModel [name=" + name + ", strength=" + strength + ", form=" + form + ", dosage=" + dosage
				+ ", frequency=" + frequency + ", route=" + route + ", duration=" + duration + ", quantity=" + quantity
				+ ", instructions=" + instructions + ", commonName=" + commonName + ", refills=" + refills
				+ ", quantityPerDay=" + quantityPerDay + "]";
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getStrength() {
		return strength;
	}
	public void setStrength(String strength) {
		this.strength = strength;
	}
	public String getForm() {
		return form;
	}
	public void setForm(String form) {
		this.form = form;
	}
	public String getDosage() {
		return dosage;
	}
	public void setDosage(String dosage) {
		this.dosage = dosage;
	}
	public String getFrequency() {
		return frequency;
	}
	public void setFrequency(String frequency) {
		this.frequency = frequency;
	}
	public String getRoute() {
		return route;
	}
	public void setRoute(String route) {
		this.route = route;
	}
	public String getDuration() {
		return duration;
	}
	public void setDuration(String duration) {
		this.duration = duration;
	}
	public String getQuantity() {
		return quantity;
	}
	public void setQuantity(String quantity) {
		this.quantity = quantity;
	}
	public String getInstructions() {
		return instructions;
	}
	public void setInstructions(String instructions) {
		this.instructions = instructions;
	}
	public String getCommonName() {
		return commonName;
	}
	public void setCommonName(String commonName) {
		this.commonName = commonName;
	}
	public String getRefills() {
		return refills;
	}
	public void setRefills(String refills) {
		this.refills = refills;
	}
	/**
	 * @return the quantityPerDay
	 */
	public String getQuantityPerDay() {
		return quantityPerDay;
	}
	/**
	 * @param quantityPerDay the quantityPerDay to set
	 */
	public void setQuantityPerDay(String quantityPerDay) {
		this.quantityPerDay = quantityPerDay;
	}
	/**
	 * @return the quantityAtTime
	 */
	public String getQuantityAtTime() {
		return quantityAtTime;
	}
	/**
	 * @param quantityAtTime the quantityAtTime to set
	 */
	public void setQuantityAtTime(String quantityAtTime) {
		this.quantityAtTime = quantityAtTime;
	}
	private String name;
	private String strength;
	private String form;
    private String dosage;
    private String frequency;
    private String route;
    private String duration;
    private String quantity;
    private String instructions;
    private String commonName;
    private String refills;
    private String quantityPerDay;
    private String quantityAtTime;


}

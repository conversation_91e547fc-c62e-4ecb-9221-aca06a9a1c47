package com.medsure.prescription.parser;

import java.util.HashMap;
import java.util.List;
import java.util.Optional;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

public class MedicationConstant {
	
	private static String medicneType = "[\r\n"
			+ "    {\r\n"
			+ "      \"name\": \"capsule\",\r\n"
			+ "      \"id\": \"1\"\r\n"
			+ "    },\r\n"
			+ "    {\r\n"
			+ "      \"name\": \"tablet\",\r\n"
			+ "      \"id\": \"1\"\r\n"
			+ "    },\r\n"
			+ "    {\r\n"
			+ "      \"name\": \"Injection\",\r\n"
			+ "      \"id\": \"2\"\r\n"
			+ "    },\r\n"
			+ "    {\r\n"
			+ "      \"name\": \"Inhaler\",\r\n"
			+ "      \"id\": \"3\"\r\n"
			+ "    },\r\n"
			+ "    {\r\n"
			+ "      \"name\": \"Nasal Spray\",\r\n"
			+ "      \"id\": \"4\"\r\n"
			+ "    },\r\n"
			+ "    {\r\n"
			+ "      \"name\": \"Syrup\",\r\n"
			+ "      \"id\": \"5\"\r\n"
			+ "    }\r\n"
			+ "  ]";
	
	public static String getMedicineType(String tablet) {
		ObjectMapper mapper = new ObjectMapper();
		try {
			List<HashMap<String, String>> detailsList =	mapper.readValue(medicneType, new TypeReference<List<HashMap<String, String>>>(){});
			Optional<String> data = detailsList.stream().filter(a->a.get("name").equalsIgnoreCase(tablet)).map(a->a.get("id")).findAny();
			return data.get();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return "0";
	}
	
	public static Integer getMedicineDays(String tablet) {
		ObjectMapper mapper = new ObjectMapper();
		try {
			List<HashMap<String, Integer>> detailsList =	mapper.readValue(medicneType, new TypeReference<List<HashMap<String, Integer>>>(){});
			Optional<Integer> data = detailsList.stream().filter(a->a.get(tablet)!=null).map(a->a.get(tablet)).findAny();
			return data.get();
		} catch (JsonProcessingException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return null;
	}
	
	public static void main(String[] args) {
		System.out.println(MedicationConstant.getMedicineType("tabletgh"));
	}

}
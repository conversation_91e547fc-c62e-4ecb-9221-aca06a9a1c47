/**
 * 
 */
package com.medsure.service;

import java.util.List;
import java.util.Map;

import com.medsure.exception.ClinicianNotFoundException;
import com.medsure.exception.WatchNotFoundException;
import com.medsure.exception.WatchRxServiceException;
import com.medsure.model.WatchrxSecCGInvite;
import com.medsure.ui.entity.access.Group;
import com.medsure.ui.entity.caregiver.request.Login;
import com.medsure.ui.entity.diyva.DiyvaDialog;
import com.medsure.ui.entity.jwt.JwtRequest;
import com.medsure.ui.entity.server.AddressVO;
import com.medsure.ui.entity.server.AssignPatientsToCareGiverVO;
import com.medsure.ui.entity.server.AssignToPatientVO;
import com.medsure.ui.entity.server.AsssignCMToPatientVO;
import com.medsure.ui.entity.server.CareGiverMinimalVO;
import com.medsure.ui.entity.server.CaregiverListVO;
import com.medsure.ui.entity.server.CaseManagerPatientsAssignVO;
import com.medsure.ui.entity.server.CaseManagerVO;
import com.medsure.ui.entity.server.ClinicianGCMVO;
import com.medsure.ui.entity.server.ClinicianPatientTimerVO;
import com.medsure.ui.entity.server.ClinicianVO;
import com.medsure.ui.entity.server.CriticalAlertsCountVO;
import com.medsure.ui.entity.server.LoggedUserNotificationVO;
import com.medsure.ui.entity.server.PatientClinician;
import com.medsure.ui.entity.server.PatientCountVO;
import com.medsure.ui.entity.server.PatientMinimalListVO;
import com.medsure.ui.entity.server.PatientWatchCaregiverListVO;
import com.medsure.ui.entity.server.PhysicianCaseManagerAssignVO;
import com.medsure.ui.entity.server.PhysicianListVO;
import com.medsure.ui.entity.server.RPMPhysicianCaseManagersResponseVO;
import com.medsure.ui.entity.server.RegistrationVO;
import com.medsure.ui.entity.server.StatusVO;
import com.medsure.ui.entity.server.TaskListVO;
import com.medsure.ui.entity.server.TasksVO;
import com.medsure.ui.entity.server.WatchrxCaseManagerPatientsAssignmentResponseListVO;
import com.medsure.ui.entity.server.WatchrxPhysicianCaseManagerAssignmentResponseListVO;
import com.medsure.ui.entity.server.Clinician.ClinicianOrgDetails;
import com.medsure.ui.entity.server.Clinician.ClinicianResponse;
import com.medsure.ui.entity.server.Clinician.ClinicianWithAssociatedWatchesAndPatientsVO;
import com.medsure.ui.entity.server.calendar.WatchRxCalendarTaskVo;

/**
 * <AUTHOR>
 *
 */
public interface ClinicianService {

	public void saveClinician(ClinicianVO clinicianVO);

	public void savePhysician(ClinicianVO cl);

	public void registerClinician(RegistrationVO clinicianVO);

	public List<ClinicianVO> getClinicianList();

	public List<ClinicianWithAssociatedWatchesAndPatientsVO> getClinicianListWithInActive();

	public List<ClinicianWithAssociatedWatchesAndPatientsVO> getClinicianListWithInActive(int index, int size);

	public Long getCount();

	public List<ClinicianWithAssociatedWatchesAndPatientsVO> getClinicianListWithInActive(String name);

	public List<ClinicianWithAssociatedWatchesAndPatientsVO> getClinicianListWithInActiveByClinicianId(
			Long clinicianId);

	public List<ClinicianVO> getClinicianList(int shiftId);

	public ClinicianVO getClinician(Long clinicianId, Long roleType);

	public String deleteClinician(Long clinicianId);

	public void deletePhysician(Long physicianId);

	public void saveGCMRegID(String gcmId, Long careGiverId, String platformType);

	public String getGCMRegID(Long careGiverId);

	public String getClinicianPlatformTypeByPatientId(Long patientId);

	public List<String> getNurseGCMRegIdByPatientId(Long patientId);

	/* Added for RabbitMQChange */
	public List<ClinicianGCMVO> getAllClinicianByPatientId(Long patientId);

	public List<ClinicianGCMVO> getAllNurseGCMRegIdByPatientId(Long patientId);

	public ClinicianVO getClinicianByUserName(String userName);

	public String getClinicianId(Long patientId);

	public Boolean isUsernameExists(ClinicianVO clinicianVO);

	public Boolean isUserExists(RegistrationVO clinicianVO);

	List<PatientClinician> getPatientNurseAssignment();

	public void unassign(long id);

	public void unassignPW(long id);

	public void resetPasswordByUserName(Login loginDetails);

	List<PatientClinician> getPatientWatchAssignment(String username);

	public ClinicianVO changeClinicianStatus(Long clinicianId);

	public List<WatchrxSecCGInvite> getSecCGInvite(String email);

	public Boolean AcceptSecondaryCaregiverInvite(AssignToPatientVO assignData);

	public Boolean RejectSecondaryCaregiverInvite(AssignToPatientVO assign);

	public String getCaregiverRelationship(Long clinicianId, Long watchId);

	public void unassignCaregiver(AssignToPatientVO data);

	// returns null if id does not belong to any clinician
	// else returns List<AddressVO>
	public List<AddressVO> getMailingAddressByClinicianID(Long id);

	// returns null if userid does not belong to any clinician
	// else returns List<AddressVO>
	public List<AddressVO> getMailingAddressByUserID(Long id);

	public Boolean haswatch(Long id);

	public List<ClinicianWithAssociatedWatchesAndPatientsVO> changeWatchStatusByWatchId(Long clinicianId, Long watchId,
			Boolean watchState) throws WatchNotFoundException, ClinicianNotFoundException;

	public List<ClinicianVO> getCaregiverInfoByuser(Long caregiverId);

//	public ClinicianResponse getPhysiciansListWithInActive();

	public ClinicianResponse getClinicianListWithInActiveNew(String userType);

	public WatchrxPhysicianCaseManagerAssignmentResponseListVO assignCaseManagerToPhysician(
			PhysicianCaseManagerAssignVO physicianCaseManagerAssignVO);

	public Long getClinicianIdForUserId(Long userId, Long roleType) throws WatchRxServiceException;

	public RPMPhysicianCaseManagersResponseVO getAllRPMPhysicianCaseManagers(Integer index, Integer pageSize);

	public Long getAllRPMCaseManagersCount();

	public Long getAllPhysiciansByCaseMangerByUserIndexCount(Long userId);

	public Long getAllCaseMangersByPhysicianByUserIndexCount(Long userId);

	public CaregiverListVO getAllCaseMangersByPhysicianByUserIndex(Long userId, Integer index, Integer pageSize);

	public PhysicianListVO getAllPhysiciansByCaseMangerByUserIndex(Long userId, Integer index, Integer pageSize);

	public PatientMinimalListVO getPatientsByCaseManger(Long userId, Long orgId);

	public PatientMinimalListVO getPatientsByCaseMangerId(Long caseManagerId);

	public PatientMinimalListVO getPatientsByPhysician(Long userId, Long orgId);

	public boolean saveTask(TasksVO tasksVO);

	public TaskListVO getFutureTasks(TasksVO tasksVO);

	public Long getDueTasksCount(Long orgId, Integer roleType, Long userId);

	public TaskListVO getDueTasks(TasksVO tasksVO);

	public Long getFutureTasksCount(Long orgId, Integer roleType, Long userId);

	public boolean deleteTask(TasksVO tasksVO);

	public PatientCountVO patientCountCaseManager(Long userId, String type);

	public PatientCountVO patientCountPhysician(Long userId, String type);

	public CriticalAlertsCountVO criticalAlertsCountCaseManager(Long userId, String type);

	public CriticalAlertsCountVO criticalAlertsCountPhysician(Long userId, String type);

	public LoggedUserNotificationVO getUserNotificationCount(Long userId, Integer roleType);

	public List<CareGiverMinimalVO> getAllCaregiversMinimal();

	public boolean assignPatientToCareGiver(AssignPatientsToCareGiverVO assignPatientsToCareGiverVO);

	public ClinicianResponse getClinicianListWithInActiveNew(String userType, Integer index, Integer pageSize);

	public List<ClinicianVO> getCaseManagersByPatient(Long patientId) throws WatchRxServiceException;

	public List<ClinicianVO> getCareGiversByPatient(Long patientId) throws WatchRxServiceException;

	public Long physicianCount();

	public TaskListVO getDueTasksNotClosed(TasksVO tasksVO);

	public Long getDueTasksNotClosedCount(Long orgId, Long userId);

	public WatchrxCaseManagerPatientsAssignmentResponseListVO assignCaseManagerToPatients(
			CaseManagerPatientsAssignVO caseManagerPatientsAssignVO);

	public WatchrxCaseManagerPatientsAssignmentResponseListVO unAssignCaseManagerToPatients(
			CaseManagerPatientsAssignVO caseManagerPatientsAssignVO);

	public PatientWatchCaregiverListVO getPatientsListForCaseManager(CaseManagerVO watchrxClinician);

	ClinicianVO changePhysicianStatus(Long clinicianId);

	public void intimatePhysicianForBillingReview();

	public boolean resentOTPByEmailId(String username);

	Long getDueTasksByDate(Long userId, Integer roleType, String date);

	void sendDialogsCommToCG(DiyvaDialog dailog);

	void sendNewDialogsToCG(DiyvaDialog dailog2);

	public Long getUserIdByClinicianId(Long userId) throws WatchRxServiceException;

	public PatientMinimalListVO getAllPatientByOrg(Long orgId, Long roleType, Long userId);

	public ClinicianVO getClinicianByUserId(Long userId, Long roleType);

	public List<Group> getGroupsAndRolesForUser(Long userId);

	public boolean expirePasswordReset(JwtRequest loginDetails);

	public PatientMinimalListVO getPatientsByCaseMangerV2(Long userId, Long orgId);

	ClinicianOrgDetails getClinicinOrgDetails();

	public void assignPatientsToNewCaseManager(AsssignCMToPatientVO caregiver);

	public List<Map<String, Object>> getAllCMByUsersByOrg(Long orgId, String st, String ed);

	public TaskListVO getAllTasksByPatientId(Integer index, Integer pageSize, Long patientId);

	public Long getAllTasksCountByPatientId(Long patientId);

	public PatientMinimalListVO getPatientsByOrgV2(Long orgId);

	public StatusVO createCalendarTask(WatchRxCalendarTaskVo calendarTaskVo);

	public List<WatchRxCalendarTaskVo> getAllCalendarTaskByUserId(Long orgId, Long userId);

	public StatusVO editCalendarTask(WatchRxCalendarTaskVo calendarTaskVo);

	public StatusVO deleteCalendarTask(Long taskId);

	public StatusVO notificationStatus(Long userId, Boolean status);

	public Map<String, Object> getNotificationStatus(Long userId);

	public void sendEmailNotifications();

	public List<WatchRxCalendarTaskVo> getTodayCalendarTasks(Long orgId, Long userId, String date);

	public List<WatchRxCalendarTaskVo> getTodayCalendarTasksV2(Long orgId, Long userId, String date,
			String userTimezone);

	public List<Map<String, Object>> getCarePlanTabs();

	public Map<Long, Object> getCarePlanQuestions();

	Long getAllRPMAdminCount();

	ClinicianVO changeAdminStatus(Long clinicianId);

	public List<WatchRxCalendarTaskVo> getTaskByPatientId(Long patientId);

	public void saveCMPatientTimer(Long userId, Long patientId, String timer);

	public List<ClinicianPatientTimerVO> fetchCMPatientTimer();

	public List<Map<String, Object>> getAllCMByUsersByOrgandName(Long orgId, String name);

	PhysicianListVO getPhysiciansByOrgAndName(Long orgId, String name, Long userId);

	public ClinicianResponse searchUserByRole(Integer role, String name);

	public Boolean isUserWithSameRoleExists(RegistrationVO vo);
}

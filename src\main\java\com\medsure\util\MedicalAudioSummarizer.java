package com.medsure.util;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import com.google.api.gax.longrunning.OperationFuture;
import com.google.auth.oauth2.ServiceAccountCredentials;
import com.google.cloud.speech.v1.LongRunningRecognizeMetadata;
import com.google.cloud.speech.v1.LongRunningRecognizeResponse;
import com.google.cloud.speech.v1.RecognitionAudio;
import com.google.cloud.speech.v1.RecognitionConfig;
import com.google.cloud.speech.v1.SpeechClient;
import com.google.cloud.speech.v1.SpeechRecognitionAlternative;
import com.google.cloud.speech.v1.SpeechRecognitionResult;
import com.google.cloud.speech.v1.SpeechSettings;
import com.google.cloud.vertexai.VertexAI;
import com.google.cloud.vertexai.api.Content;
import com.google.cloud.vertexai.api.GenerateContentResponse;
import com.google.cloud.vertexai.api.GenerationConfig;
import com.google.cloud.vertexai.generativeai.GenerativeModel;
import com.google.cloud.vertexai.generativeai.ResponseStream;
import com.google.protobuf.ByteString;
import com.medsure.model.WatchrxAudio;
import com.medsure.service.impl.PatientServiceImpl;

public class MedicalAudioSummarizer {
    private static final Logger logger = LoggerFactory.getLogger(PatientServiceImpl.class);
    
    private static final Pattern FOOTER_PATTERN = Pattern.compile("— Time Spent: \\d+ minutes \\| Escalation: (EMERGENT|URGENT|ROUTINE|NONE)$");
    private static final Pattern NUMBER_PATTERN = Pattern.compile("\\b(\\d{2,4})\\b");
    private static final Pattern QUOTE_PATTERN = Pattern.compile("\"([^\"]{3,200})\"");
    
    private static final List<String> FORBIDDEN_PHRASES = Arrays.asList(
        "suggestive of", "likely", "rule out", "possible infection", 
        "diagnosis", "prognosis", "recommend changing medication"
    );
    
    public enum Escalation {
        EMERGENT, URGENT, ROUTINE, NONE
    }
    
    public static class ValidationException extends Exception {
        public ValidationException(String message) {
            super(message);
        }
    }

    public String summarizeData(String audioPath, WatchrxAudio audio) throws Exception {
        String projectId = "watchrx-1007";
        Resource resource = new ClassPathResource("secrets/watchrx-1007-cecd894ddd24.json");
        File file = resource.getFile();
        String absolutePath = file.getAbsolutePath();

        String transcript = transcribeAudio(audioPath, absolutePath);

        audio.setAudioText(transcript.trim());

        if (transcript == null || transcript.trim().isEmpty()) {
            logger.warn("No transcript found in audio file, skipping summarization");
            String noTranscriptMessage = "No transcript available - audio file did not contain recognizable speech";
            audio.setSummarizeAudioText(noTranscriptMessage);

            String validationChecksJson = "{\"hasTranscript\":\"false\",\"overallValid\":\"false\"}";
            audio.setValidationChecks(validationChecksJson);

            return noTranscriptMessage;
        }

        String summary = summarizeText(transcript, projectId, absolutePath);

        // Check if the model identified the transcript as invalid/non-medical
        if (summary.startsWith("INVALID_TRANSCRIPT:")) {
            logger.warn("Model identified non-medical transcript: {}", summary);
            audio.setSummarizeAudioText(summary);

            // Set validation checks to indicate invalid transcript
            String validationChecksJson = "{\"isValidMedicalTranscript\":\"false\",\"overallValid\":\"false\"}";
            audio.setValidationChecks(validationChecksJson);

            return summary;
        }

        String validationChecksJson = validateSummary(summary, transcript);

        audio.setValidationChecks(validationChecksJson);
        audio.setSummarizeAudioText(summary.trim());
        return summary;
    }

    public static String transcribeAudio(String audioPath, String keyPath) throws Exception {
        SpeechSettings settings = SpeechSettings.newBuilder()
                .setCredentialsProvider(() ->
                        ServiceAccountCredentials.fromStream(new FileInputStream(keyPath)))
                .build();

        try (SpeechClient speechClient = SpeechClient.create(settings)) {
            RecognitionConfig config = RecognitionConfig.newBuilder()
                    .setEncoding(RecognitionConfig.AudioEncoding.LINEAR16)
                    .setSampleRateHertz(16000)
                    .setLanguageCode("en-US")
                    .setModel("medical")
                    .setEnableAutomaticPunctuation(true)
                    .setUseEnhanced(true)
                    .build();

            RecognitionAudio audio;

            if (audioPath.startsWith("gs://")) {
                audio = RecognitionAudio.newBuilder()
                        .setUri(audioPath)
                        .build();
            } else {
                ByteString audioBytes;

                if (audioPath.startsWith("https://")) {
                    URL url = new URL(audioPath);
                    HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                    connection.setRequestMethod("GET");

                    try (InputStream inputStream = connection.getInputStream();
                         ByteArrayOutputStream buffer = new ByteArrayOutputStream()) {

                        byte[] data = new byte[4096];
                        int nRead;
                        while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
                            buffer.write(data, 0, nRead);
                        }
                        audioBytes = ByteString.copyFrom(buffer.toByteArray());
                    }

                } else {
                    audioBytes = ByteString.copyFrom(Files.readAllBytes(Path.of(audioPath)));
                }

                audio = RecognitionAudio.newBuilder()
                        .setContent(audioBytes)
                        .build();
            }

            // Use LongRunningRecognize for all inputs
            OperationFuture<LongRunningRecognizeResponse, LongRunningRecognizeMetadata> response =
                    speechClient.longRunningRecognizeAsync(config, audio);

            LongRunningRecognizeResponse result = response.get(5, TimeUnit.MINUTES);

            StringBuilder transcript = new StringBuilder();
            for (SpeechRecognitionResult res : result.getResultsList()) {
                for (SpeechRecognitionAlternative alt : res.getAlternativesList()) {
                    transcript.append(alt.getTranscript()).append(" ");
                }
            }

            String finalTranscript = transcript.toString().trim();

            return finalTranscript;
        }
    }

    public static String summarizeText(String transcript, String projectId, String keyPath) throws Exception {
        String modelName = "gemini-2.5-flash";

        try (VertexAI vertexAI = new VertexAI(projectId, "us-central1")) {
            GenerationConfig generationConfig = GenerationConfig.newBuilder()
                    .setTemperature(0.1f)
                    .setMaxOutputTokens(2000)
                    .setTopP(0.8f)
                    .build();

            GenerativeModel model = new GenerativeModel.Builder()
                    .setModelName(modelName)
                    .setVertexAi(vertexAI)
                    .setGenerationConfig(generationConfig)
                    .build();

            logger.info("Sending prompt to Gemini model: " + modelName);
            String medicalPrompt = buildPrompt(transcript);

            Content promptContent = Content.newBuilder().setRole("user")
                    .addParts(com.google.cloud.vertexai.api.Part.newBuilder().setText(medicalPrompt).build())
                    .build();

            ResponseStream<GenerateContentResponse> responseStream = model.generateContentStream(promptContent);
            StringBuilder sb = new StringBuilder();
            for (GenerateContentResponse chunk : responseStream) {
                if (chunk.getCandidatesCount() > 0) {
                    for (com.google.cloud.vertexai.api.Part part : chunk.getCandidates(0).getContent().getPartsList()) {
                        if (part.hasText()) {
                            sb.append(part.getText());
                        }
                    }
                }
            }
            
            logger.info("FINAL_STRING_****{}", sb.toString());

            String finalResponse = sb.toString().trim();

            // Check if the model identified the transcript as invalid
            if (finalResponse.startsWith("INVALID_TRANSCRIPT:")) {
                logger.warn("Model identified invalid transcript: {}", finalResponse);
                return finalResponse;
            }

            return finalResponse;
        } catch (IOException e) {
            System.err.println("Error interacting with Vertex AI: " + e.getMessage());
            return e.getMessage();
        }
    }
    
    private static String buildPrompt(String transcript) {
        return String.format("""
            You are a Certified Medical Assistant (CMA) serving as a remote Care Manager for Chronic Care Management (CCM) and Remote Patient Monitoring (RPM), working under the supervision of the ordering provider.

            CRITICAL VALIDATION REQUIREMENTS:
            1. **ONLY process medical patient-caregiver conversations** - If the transcript is not a medical encounter, respond EXACTLY with: "INVALID_TRANSCRIPT: Not a medical patient-caregiver conversation"
            2. **NEVER generate fictional content** - If information is unclear or missing, state "information not provided" rather than creating details
            3. **STRICT adherence to transcript content** - Only document what was explicitly stated in the conversation
            4. **NO medical advice or clinical decisions** - You are documenting, not diagnosing or treating

            TRANSCRIPT VALIDATION CHECKLIST:
            - Must contain patient-caregiver medical discussion
            - Must have health-related content (symptoms, medications, vital signs, care plans)
            - Must NOT be: casual conversation, business call, technical support, non-medical content

            If transcript passes validation, craft a clear, HIPAA‑compliant, present‑tense clinical-encounter summary.

            Documentation style & tone
                • Professional, concise, empathetic, and evidence-based
                • Use standard medical terminology appropriately
                • Write a single flowing narrative paragraph (no bullet lists) that reads naturally to a clinician
                • **ABSOLUTELY NO diagnoses, lab interpretations, care plan alterations, or independent clinical decisions**

            Mandatory narrative elements (ONLY if present in transcript)
                1. **Opening context** - Who initiated the call/visit, when, and why
                2. **Patient quotations** - Use exact quotes from transcript with proper attribution ("Patient states...")
                3. **Current status** - Symptoms, self-reported vital signs, device readings, medication adherence as explicitly described
                4. **Care-management actions** - Only actions actually performed during this encounter
                5. **Barriers to care** - Only those specifically mentioned by patient/caregiver
                6. **Escalation level** - Assign ONLY if clear criteria are met based on transcript content:
                    • **EMERGENT**: BP > 180/120, BG < 50 or > 400 mg/dL, severe SOB, chest pain, syncope, acute neurological changes
                    • **URGENT**: BP ≥ 160/100 on repeated readings, BG 300-400 mg/dL with symptoms, 2-5 lb CHF weight gain in 2-3 days, escalating COPD symptoms, missed dialysis
                    • **ROUTINE**: Mild deviations or questions warranting provider review within 24-48 hours
                7. **Follow-up plan** - Only plans explicitly discussed and agreed upon
                8. **Provider-review items** - Only discrepancies or concerns explicitly mentioned

            ABSOLUTE PROHIBITIONS
                • NO assumptions or inferences beyond transcript content
                • NO external medical knowledge or general health advice
                • NO fictional details, names, dates, or medical values not in transcript
                • NO diagnostic statements or medical interpretations
                • NO recommendations not explicitly discussed in the conversation

            Required output format
                If valid medical transcript: Write **one cohesive paragraph**, then add:
                — Escalation: [EMERGENT/URGENT/ROUTINE/NONE]

                If invalid transcript: Respond exactly with:
                "INVALID_TRANSCRIPT: Not a medical patient-caregiver conversation"

            Patient-Caregiver Conversation Transcript:
            %s""", transcript);
    }

    private static String validateSummary(String summary, String transcript) {

        if (summary == null || summary.trim().isEmpty()) {
            return "{\"validationError\":\"Summary is null or empty\",\"overallValid\":\"false\"}";
        }

        StringBuilder jsonBuilder = new StringBuilder();
        jsonBuilder.append("{");

        try {
            // 1. Footer + basic format validation
            boolean hasValidFooter = FOOTER_PATTERN.matcher(summary).find();
            jsonBuilder.append("\"hasValidFooter\":\"").append(hasValidFooter).append("\",");

            int footerIndex = summary.lastIndexOf("— Time Spent");
            if (footerIndex == -1) {
                footerIndex = summary.length();
            }

            String body = summary.substring(0, footerIndex);

            // 2. Single paragraph validation
            int newlineCount = body.length() - body.replace("\n", "").length();
            boolean isSingleParagraph = newlineCount <= 1;
            jsonBuilder.append("\"isSingleParagraph\":\"").append(isSingleParagraph).append("\",");

            // 3. Length validation
            boolean isValidLength = summary.length() <= 4096;
            jsonBuilder.append("\"isValidLength\":\"").append(isValidLength).append("\",");

            // 4. Quote validation (reuse existing helper method)
            String quoteIssue = checkQuotes(body, transcript);
            boolean hasValidQuotes = quoteIssue == null;
            jsonBuilder.append("\"hasValidQuotes\":\"").append(hasValidQuotes).append("\",");

            // 5. Number validation (reuse existing helper method)
            String numberIssue = checkNumbers(body, transcript);
            boolean hasValidNumbers = numberIssue == null;
            jsonBuilder.append("\"hasValidNumbers\":\"").append(hasValidNumbers).append("\",");

            // 6. Forbidden phrases check
            boolean hasNoForbiddenPhrases = true;
            for (String phrase : FORBIDDEN_PHRASES) {
                if (body.toLowerCase().contains(phrase.toLowerCase())) {
                    hasNoForbiddenPhrases = false;
                    break;
                }
            }
            jsonBuilder.append("\"hasNoForbiddenPhrases\":\"").append(hasNoForbiddenPhrases).append("\",");

            boolean overallValid = hasValidFooter && isSingleParagraph && isValidLength &&
                                  hasValidQuotes && hasValidNumbers && hasNoForbiddenPhrases;
            jsonBuilder.append("\"overallValid\":\"").append(overallValid).append("\"");

        } catch (Exception e) {
            logger.error("Summary validation error: {}", e.getMessage());
            jsonBuilder.append("\"validationError\":\"").append(e.getMessage()).append("\",");
            jsonBuilder.append("\"overallValid\":\"false\"");
        }

        jsonBuilder.append("}");

        String validationJson = jsonBuilder.toString();

        return validationJson;
    }
    
    private static String checkQuotes(String body, String transcript) {
        try {
            Matcher matcher = QUOTE_PATTERN.matcher(body);
            while (matcher.find()) {
                String quote = matcher.group(1).trim();
                String normalizedQuote = quote.replaceAll("\\s+", " ").toLowerCase();
                String normalizedTranscript = transcript.toLowerCase();
                
                if (normalizedQuote.length() >= 5 && !normalizedTranscript.contains(normalizedQuote)) {
                    return "Quote not in transcript";
                }
            }
            return null;
        } catch (Exception e) {
            logger.warn("Quote check error: {}", e.getMessage());
            return null;
        }
    }
    
    private static String checkNumbers(String body, String transcript) {
        try {
            Set<String> transcriptNumbers = new HashSet<>();
            Matcher transcriptMatcher = NUMBER_PATTERN.matcher(transcript);
            while (transcriptMatcher.find()) {
                transcriptNumbers.add(transcriptMatcher.group(1));
            }

            Matcher bodyMatcher = NUMBER_PATTERN.matcher(body);
            while (bodyMatcher.find()) {
                String number = bodyMatcher.group(1);
                if (Integer.parseInt(number) > 10 && !transcriptNumbers.contains(number)) {
                    return "Number " + number + " not in transcript";
                }
            }
            
            return null;
        } catch (Exception e) {
            logger.warn("Number check error: {}", e.getMessage());
            return null;
        }
    }
}
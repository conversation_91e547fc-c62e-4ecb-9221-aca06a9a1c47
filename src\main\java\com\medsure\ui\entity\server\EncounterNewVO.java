package com.medsure.ui.entity.server;

import java.io.Serializable;

public class EncounterNewVO implements Serializable {
	private static final long serialVersionUID = 1L;
	private Long patientId;
	private Long encounterId;
	private Long patientAlertId;
	private Double duration;
//	private String encounterTitle;

	private String encounterReason;
	private String reasonCode;


	private String encounterDescription;
	private String encounterDateTime;
	private String encounterEndDateTime;
//	private String encounterResult;

	private String addedByUser;
	private Long userId;

	private long enMins;
	private String review;
	private long rpmMins;
	private long ccmMins;
	private long pcmMins;

	private String durationInMS;

	private Boolean isDraft = false;

	public Long getPatientAlertId() {
		return patientAlertId;
	}

	public void setPatientAlertId(Long patientAlertId) {
		this.patientAlertId = patientAlertId;
	}

	public String getAddedByUser() {
		return addedByUser;
	}

	public void setAddedByUser(String addedByUser) {
		this.addedByUser = addedByUser;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Long getPatientId() {
		return patientId;
	}

	public void setPatientId(Long patientId) {
		this.patientId = patientId;
	}

	public Long getEncounterId() {
		return encounterId;
	}

	public void setEncounterId(Long encounterId) {
		this.encounterId = encounterId;
	}

//	public String getEncounterTitle() {
//		return encounterTitle;
//	}
//
//	public void setEncounterTitle(String encounterTitle) {
//		this.encounterTitle = encounterTitle;
//	}

	public String getEncounterReason() {
		return encounterReason;
	}

	public void setEncounterReason(String encounterReason) {
		this.encounterReason = encounterReason;
	}

	public String getEncounterDescription() {
		return encounterDescription;
	}

	public void setEncounterDescription(String encounterDescription) {
		this.encounterDescription = encounterDescription;
	}

	public String getEncounterDateTime() {
		return encounterDateTime;
	}

	public void setEncounterDateTime(String encounterDateTime) {
		this.encounterDateTime = encounterDateTime;
	}

	public String getEncounterEndDateTime() {
		return encounterEndDateTime;
	}

	public void setEncounterEndDateTime(String encounterEndDateTime) {
		this.encounterEndDateTime = encounterEndDateTime;
	}

	/**
	 * @return the duration
	 */
	public Double getDuration() {
		return duration;
	}

	/**
	 * @param duration the duration to set
	 */
	public void setDuration(Double duration) {
		this.duration = duration;
	}

	/**
	 * @return the enMins
	 */
	public long getEnMins() {
		return enMins;
	}

	/**
	 * @param enMins the enMins to set
	 */
	public void setEnMins(long enMins) {
		this.enMins = enMins;
	}

	/**
	 * @return the review
	 */
	public String getReview() {
		return review;
	}

	/**
	 * @param review the review to set
	 */
	public void setReview(String review) {
		this.review = review;
	}

	/**
	 * @return the rpmMins
	 */
	public long getRpmMins() {
		return rpmMins;
	}

	/**
	 * @param rpmMins the rpmMins to set
	 */
	public void setRpmMins(long rpmMins) {
		this.rpmMins = rpmMins;
	}

	/**
	 * @return the ccmMins
	 */
	public long getCcmMins() {
		return ccmMins;
	}

	/**
	 * @param ccmMins the ccmMins to set
	 */
	public void setCcmMins(long ccmMins) {
		this.ccmMins = ccmMins;
	}

	/**
	 * @return the pcmMins
	 */
	public long getPcmMins() {
		return pcmMins;
	}

	/**
	 * @param pcmMins the pcmMins to set
	 */
	public void setPcmMins(long pcmMins) {
		this.pcmMins = pcmMins;
	}

	public String getDurationInMS() {
		return durationInMS;
	}

	public void setDurationInMS(String durationInMS) {
		this.durationInMS = durationInMS;
	}

//	public String getEncounterResult() {
//		return encounterResult;
//	}
//
//	public void setEncounterResult(String encounterResult) {
//		this.encounterResult = encounterResult;
//	}
	public String getReasonCode() {
		return reasonCode;
	}

	public void setReasonCode(String reasonCode) {
		this.reasonCode = reasonCode;
	}

	public Boolean getIsDraft() {return isDraft; }

	public void setIsDraft(Boolean isDraft) { this.isDraft = isDraft; }

}

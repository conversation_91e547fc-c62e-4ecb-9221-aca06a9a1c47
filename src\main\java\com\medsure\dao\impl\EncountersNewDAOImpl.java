package com.medsure.dao.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Calendar;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.Query;

import org.springframework.stereotype.Component;

import com.medsure.dao.EncountersNewDAO;
import com.medsure.model.WatchRxEncountersNew;

@Component
public class EncountersNewDAOImpl extends BaseDAOImpl<WatchRxEncountersNew> implements EncountersNewDAO {
	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> homeVisitsCountWeekPhysician(Long physicianId) {

		EntityManager em = entityManagerFactory.createEntityManager();

		Query q = em.createQuery(
				"SELECT DAYOFWEEK(a.encounterDatetime),COUNT(a) FROM WatchRxEncountersNew a  WHERE a.encounterReason= 'Home Visits' AND a.watchrxPatient.patientId IN "
						+ "(SELECT b.patientId FROM WatchrxPatient b WHERE b.watchrxPhysician.physicianId= :physicianId) "
						+ "AND YEAR(a.encounterDatetime) = YEAR(CURDATE()) AND WEEK(a.encounterDatetime) = WEEK(CURDATE())"
						+ "GROUP BY DAYOFWEEK(a.encounterDatetime)");
		q.setParameter("physicianId", physicianId);
		List<Object[]> result = q.getResultList();

		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> homeVisitsCountMonthPhysician(Long physicianId) {

		EntityManager em = entityManagerFactory.createEntityManager();

		Query q = em.createQuery(
				"SELECT DAYOFMONTH(a.encounterDatetime),COUNT(a) FROM WatchRxEncountersNew a  WHERE a.encounterReason= 'Home Visits' AND a.watchrxPatient.patientId IN "
						+ "(SELECT b.patientId FROM WatchrxPatient b WHERE b.watchrxPhysician.physicianId= :physicianId) "
						+ "AND YEAR(a.encounterDatetime) = YEAR(CURDATE()) AND MONTH(a.encounterDatetime) = MONTH(CURDATE()) "
						+ "GROUP BY DAYOFMONTH(a.encounterDatetime)");
		q.setParameter("physicianId", physicianId);
		List<Object[]> result = q.getResultList();

		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> homeVisitsCountYearPhysician(Long physicianId) {

		EntityManager em = entityManagerFactory.createEntityManager();

		Query q = em.createQuery(
				"SELECT MONTH(a.encounterDatetime),COUNT(a) FROM WatchRxEncountersNew a  WHERE a.encounterReason= 'Home Visits' AND a.watchrxPatient.patientId IN "
						+ "(SELECT b.patientId FROM WatchrxPatient b WHERE b.watchrxPhysician.physicianId= :physicianId) "
						+ "AND YEAR(a.encounterDatetime) = YEAR(CURDATE()) " + "GROUP BY MONTH(a.encounterDatetime)");
		q.setParameter("physicianId", physicianId);
		List<Object[]> result = q.getResultList();

		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> homeVisitsCountYearCaseManager(Long clinicianId) {

		EntityManager em = entityManagerFactory.createEntityManager();

		Query q = em.createQuery(
				"SELECT MONTH(a.encounterDatetime),COUNT(a) FROM WatchRxEncountersNew a WHERE a.encounterReason= 'Home Visits' AND a.watchrxPatient.patientId IN "
						+ "(SELECT b.watchrxPatient.patientId FROM WatchrxPatientClinicianAssignmnt b WHERE b.watchrxClinician.clinicianId = :clinicianId)"
						+ "AND YEAR(a.encounterDatetime) = YEAR(CURDATE()) " + "GROUP BY MONTH(a.encounterDatetime)");
		q.setParameter("clinicianId", clinicianId);
		List<Object[]> result = q.getResultList();

		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> homeVisitsCountMonthCaseManager(Long clinicianId) {

		EntityManager em = entityManagerFactory.createEntityManager();

		Query q = em.createQuery(
				"SELECT DAYOFMONTH(a.encounterDatetime),COUNT(a) FROM WatchRxEncountersNew a  WHERE a.encounterReason= 'Home Visits' AND a.watchrxPatient.patientId IN "
						+ "(SELECT b.watchrxPatient.patientId FROM WatchrxPatientClinicianAssignmnt b WHERE b.watchrxClinician.clinicianId = :clinicianId)"
						+ "AND YEAR(a.encounterDatetime) = YEAR(CURDATE()) AND MONTH(a.encounterDatetime) = MONTH(CURDATE()) "
						+ "GROUP BY DAYOFMONTH(a.encounterDatetime)");
		q.setParameter("clinicianId", clinicianId);
		List<Object[]> result = q.getResultList();

		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> homeVisitsCountWeekCaseManager(Long clinicianId) {

		EntityManager em = entityManagerFactory.createEntityManager();

		Query q = em.createQuery(
				"SELECT DAYOFWEEK(a.encounterDatetime),COUNT(a) FROM WatchRxEncountersNew a  WHERE a.encounterReason= 'Home Visits' AND a.watchrxPatient.patientId IN "
						+ "(SELECT b.watchrxPatient.patientId FROM WatchrxPatientClinicianAssignmnt b WHERE b.watchrxClinician.clinicianId = :clinicianId)"
						+ "AND YEAR(a.encounterDatetime) = YEAR(CURDATE()) AND WEEK(a.encounterDatetime) = WEEK(CURDATE()) "
						+ "GROUP BY DAYOFWEEK(a.encounterDatetime)");
		q.setParameter("clinicianId", clinicianId);
		List<Object[]> result = q.getResultList();

		return result;
	}

	@Override
	@SuppressWarnings("unchecked")
	public List<WatchRxEncountersNew> reasonByPatientId(Long patientId, String reason, Date startDate, Date endDate) {
		EntityManager em = entityManagerFactory.createEntityManager();
		Query q = null;
		if (reason != null && reason.equalsIgnoreCase("all")) {
			q = em.createQuery("SELECT a FROM WatchRxEncountersNew a WHERE a.watchrxPatient.patientId = :patientId "
					+ " AND a.isDraft = false AND a.encounterDatetime BETWEEN :startDate AND :endDate");
			q.setParameter("patientId", patientId);
			q.setParameter("startDate", startDate);
			q.setParameter("endDate", endDate);
		} else {
			q = em.createQuery(
					"SELECT a FROM WatchRxEncountersNew a WHERE a.watchrxPatient.patientId = :patientId AND a.encounterReason LIKE CONCAT(:reason,'%')"
							+ " AND a.isDraft = false AND a.encounterDatetime BETWEEN :startDate AND :endDate");
			q.setParameter("reason", reason);
			q.setParameter("patientId", patientId);
			q.setParameter("startDate", startDate);
			q.setParameter("endDate", endDate);
		}
		List<WatchRxEncountersNew> result = q.getResultList();
		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxEncountersNew> encounterMinutesTimeIntervalPatient(Long patientId, Date startDate,
			Date endDate) {
		List<WatchRxEncountersNew> result = new ArrayList<>();
		try {
			EntityManager em = entityManagerFactory.createEntityManager();

			Query q = em
					.createQuery("SELECT a FROM WatchRxEncountersNew a  WHERE a.watchrxPatient.patientId= :patientId "
							+ " AND a.isDraft = false AND a.encounterDatetime BETWEEN :startDate AND :endDate ");
			q.setParameter("patientId", patientId).setParameter("startDate", startDate).setParameter("endDate",
					endDate);

			result = q.getResultList();
		} catch (Exception e) {

		}
		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxEncountersNew> getAllEncounterByPatientdIdAndDatePagination(Long patientId, Date startDate,
			Date endDate, Integer index, Integer pageSize) {
		List<WatchRxEncountersNew> result = new ArrayList<>();
		try {
			EntityManager em = entityManagerFactory.createEntityManager();
			Query q = em
					.createQuery("SELECT a FROM WatchRxEncountersNew a WHERE a.watchrxPatient.patientId= :patientId "
							+ "AND a.encounterDatetime BETWEEN :startDate AND :endDate " +
							" AND a.isDraft = false ORDER BY a.encounterDatetime DESC")
					.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("patientId", patientId)
					.setParameter("startDate", startDate).setParameter("endDate", endDate);
			result = q.getResultList();
		} catch (Exception e) {

		}
		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxEncountersNew> getMins(Long patientId) {
		EntityManager em = entityManagerFactory.createEntityManager();
		Query q = em.createQuery("SELECT a FROM WatchRxEncountersNew a WHERE a.watchrxPatient.patientId =:patientId "
				+ " AND a.isDraft = false AND YEAR(a.encounterDatetime) = YEAR(CURDATE()) AND MONTH(a.encounterDatetime) = MONTH(CURDATE())");
		q.setParameter("patientId", patientId);
		List<WatchRxEncountersNew> result = q.getResultList();
		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<String> getDeviceTrainingEncounterReasons(Long patientId, Date startDate, Date endDate) {
		List<String> result = new ArrayList<>();
		EntityManager em = null;

		try {
			em = entityManagerFactory.createEntityManager();
			Query query = em.createQuery("SELECT DISTINCT r.reason " + "FROM WatchRxEncountersNew e "
					+ "JOIN e.watchRxEncounterReasons r " + "WHERE e.watchrxPatient.patientId = :patientId "
					+ " AND e.isDraft = false AND e.encounterDatetime BETWEEN :startDate AND :endDate "
					+ "AND r.code IN ('EC_008','EC_009','EC_010','EC_011')"

			).setParameter("patientId", patientId).setParameter("startDate", startDate).setParameter("endDate",
					endDate);

			@SuppressWarnings("unchecked")
			List<String> queryResult = query.getResultList();
			result = queryResult != null ? queryResult : Collections.emptyList();

		} catch (Exception e) {

		} finally {
			if (em != null && em.isOpen()) {
				em.close();
			}
		}
		return result;
	}

	@Override
	public List<WatchRxEncountersNew> getEncounterForCaregiverApp(Long patientId, Date startDate, Date endDate,
			Integer index, Integer pageSize, String program) {
		List<WatchRxEncountersNew> result = new ArrayList<>();
		EntityManager em = null;
		try {
			em = entityManagerFactory.createEntityManager();
			Query q = em
					.createQuery("SELECT a FROM WatchRxEncountersNew a WHERE a.watchrxPatient.patientId= :patientId "
							+ " AND a.isDraft = false AND a.encounterDatetime BETWEEN :startDate AND :endDate AND a.review = :program ORDER BY a.encounterDatetime DESC")
					.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("patientId", patientId)
					.setParameter("startDate", startDate).setParameter("endDate", endDate)
					.setParameter("program", program);
			result = q.getResultList();
		} catch (Exception e) {

		} finally {
			if (em != null && em.isOpen()) {
				em.close();
			}
		}
		return result;
	}

	@Override
	public boolean existsByPatientAndTimeRange(Long patientId, Date startTime, Date endTime, Long excludeEncounterId) {
		EntityManager em = null;
		try {
			em = entityManagerFactory.createEntityManager();

			String queryStr = "SELECT COUNT(e) FROM WatchRxEncountersNew e WHERE " +
					"e.watchrxPatient.patientId = :patientId AND " +
					"e.isDraft = false AND " +
					"(:excludeEncounterId IS NULL OR e.encountersId != :excludeEncounterId) AND " +
					"((e.encounterDatetime < :endTime AND e.encounterEndDatetime > :startTime)" +
					" OR (e.encounterDatetime = :startTime OR e.encounterEndDatetime = :endTime))";

			Query q = em.createQuery(queryStr)
					.setParameter("patientId", patientId)
					.setParameter("startTime", startTime)
					.setParameter("endTime", endTime)
					.setParameter("excludeEncounterId", excludeEncounterId);

			Long count = (Long) q.getSingleResult();
			return count > 0;
		} catch (Exception e) {
			return false;
		} finally {
			if (em != null && em.isOpen()) {
				em.close();
			}
		}
	}

	@Override
	public boolean existsByUserAndTimeRange(Long userId,Date startTime,Date endTime,Long excludeEncounterId){
		EntityManager em = null;
		try {
			em = entityManagerFactory.createEntityManager();

			String queryStr = "SELECT COUNT(e) FROM WatchRxEncountersNew e WHERE " +
					"e.watchrxUser.userId = :userId AND " +
					"e.isDraft = false AND " +
					"(:excludeEncounterId IS NULL OR e.encountersId != :excludeEncounterId) AND " +
					"((e.encounterDatetime < :endTime AND e.encounterEndDatetime > :startTime)" +
					" OR (e.encounterDatetime = :startTime OR e.encounterEndDatetime = :endTime))";

			Query q = em.createQuery(queryStr)
					.setParameter("userId", userId)
					.setParameter("startTime", startTime)
					.setParameter("endTime", endTime)
					.setParameter("excludeEncounterId", excludeEncounterId);

			Long count = (Long) q.getSingleResult();
			return count > 0;
		} catch (Exception e) {
			return false;
		} finally {
			if (em != null && em.isOpen()) {
				em.close();
			}
		}
	}

	@Override
	public List<WatchRxEncountersNew> getAllDraftsByPatientIdAndDatePagination(Long patientId, Date startDate, Date endDate, int index, int pageSize) {
		List<WatchRxEncountersNew> result = new ArrayList<>();
		EntityManager em = null;
		try {
			Calendar cal = Calendar.getInstance();
			cal.setTime(endDate);
			cal.set(Calendar.HOUR_OF_DAY, 23);
			cal.set(Calendar.MINUTE, 59);
			cal.set(Calendar.SECOND, 59);
			cal.set(Calendar.MILLISECOND, 999);
			Date endOfDay = cal.getTime();
			em = entityManagerFactory.createEntityManager();
			Query q = em.createQuery(
					"SELECT e FROM WatchRxEncountersNew e WHERE e.watchrxPatient.patientId = :patientId AND " +
							"e.encounterDatetime BETWEEN :startDate AND :endDate AND e.isDraft = true ORDER BY e.encounterDatetime DESC");
			q.setParameter("patientId", patientId);
			q.setParameter("startDate", startDate);
			q.setParameter("endDate", endOfDay);
			q.setFirstResult(index * pageSize);
			q.setMaxResults(pageSize);

			result = q.getResultList();
		} catch (NoResultException e) {
			return new ArrayList<>();
		} finally {
			if (em != null && em.isOpen()) {
				em.close();
			}
		}
		return result;
	}

	@Override
	public Long countAllDraftsByPatientIdAndDate(Long patientId, Date startDate, Date endDate) {
		EntityManager em = null;
		try {
			Calendar cal = Calendar.getInstance();
			cal.setTime(endDate);
			cal.set(Calendar.HOUR_OF_DAY, 23);
			cal.set(Calendar.MINUTE, 59);
			cal.set(Calendar.SECOND, 59);
			cal.set(Calendar.MILLISECOND, 999);
			Date endOfDay = cal.getTime();
			em = entityManagerFactory.createEntityManager();
			Query query = em.createQuery(
					"SELECT COUNT(d) FROM WatchRxEncountersNew d " +
							"WHERE d.watchrxPatient.patientId = :patientId " +
							"AND d.encounterDatetime BETWEEN :startDate AND :endDate AND d.isDraft = true");

			query.setParameter("patientId", patientId);
			query.setParameter("startDate", startDate);
			query.setParameter("endDate", endOfDay);

			return (Long) query.getSingleResult();
		} finally {
			if (em != null && em.isOpen()) {
				em.close();
			}
		}
	}
}
